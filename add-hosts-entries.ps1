# Simple PowerShell script to add hosts entries
$hostsFile = "C:\Windows\System32\drivers\etc\hosts"

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsIn<PERSON><PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script needs to be run as Administrator!" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit
}

Write-Host "Adding 51Club domains to hosts file..." -ForegroundColor Green

# Add entries to hosts file
$entries = @"

# 51Club Casino Local Development
127.0.0.1    51club.local
127.0.0.1    www.51club.local
127.0.0.1    api.51club.local
127.0.0.1    games.51club.local
127.0.0.1    admin.51club.local
127.0.0.1    db.51club.local
"@

Add-Content -Path $hostsFile -Value $entries
Write-Host "Hosts file updated!" -ForegroundColor Green

Write-Host "Flushing DNS cache..." -ForegroundColor Yellow
ipconfig /flushdns | Out-Null

Write-Host "Done! You can now access:" -ForegroundColor Green
Write-Host "- http://51club.local:8085" -ForegroundColor Cyan
Write-Host "- http://api.51club.local:8085" -ForegroundColor Cyan
Write-Host "- http://games.51club.local:8085" -ForegroundColor Cyan
Write-Host "- http://admin.51club.local:8085" -ForegroundColor Cyan
Write-Host "- http://db.51club.local:8085" -ForegroundColor Cyan

Read-Host "Press Enter to continue"
