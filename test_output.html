<!doctype html>
<html lang="en" dir="ltr">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="referrer" content="same-origin">
  <meta name="robots" content="noindex,nofollow,notranslate">
  <meta name="google" content="notranslate">
  <style id="cfs-style">html{display: none;}</style>
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
  <link rel="stylesheet" type="text/css" href="./themes/pmahomme/jquery/jquery-ui.css">
  <link rel="stylesheet" type="text/css" href="js/vendor/codemirror/lib/codemirror.css?v=5.2.2">
  <link rel="stylesheet" type="text/css" href="js/vendor/codemirror/addon/hint/show-hint.css?v=5.2.2">
  <link rel="stylesheet" type="text/css" href="js/vendor/codemirror/addon/lint/lint.css?v=5.2.2">
  <link rel="stylesheet" type="text/css" href="./themes/pmahomme/css/theme.css?v=5.2.2">
  <title>db.51club.local / mysql | phpMyAdmin 5.2.2</title>
    <script data-cfasync="false" type="text/javascript" src="js/vendor/jquery/jquery.min.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/vendor/jquery/jquery-migrate.min.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/vendor/sprintf.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/dist/ajax.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/dist/keyhandler.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/vendor/jquery/jquery-ui.min.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/dist/name-conflict-fixes.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/vendor/bootstrap/bootstrap.bundle.min.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/vendor/js.cookie.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/vendor/jquery/jquery.validate.min.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/vendor/jquery/jquery-ui-timepicker-addon.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/vendor/jquery/jquery.debounce-1.0.6.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/dist/menu_resizer.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/dist/cross_framing_protection.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/messages.php?l=en&v=5.2.2&lang=en"></script>
  <script data-cfasync="false" type="text/javascript" src="js/dist/config.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/dist/doclinks.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/dist/functions.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/dist/navigation.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/dist/indexes.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/dist/common.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/dist/page_settings.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/dist/home.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/vendor/codemirror/lib/codemirror.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/vendor/codemirror/mode/sql/sql.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/vendor/codemirror/addon/runmode/runmode.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/vendor/codemirror/addon/hint/show-hint.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/vendor/codemirror/addon/hint/sql-hint.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/vendor/codemirror/addon/lint/lint.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/dist/codemirror/addon/lint/sql-lint.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/vendor/tracekit.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/dist/error_report.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/dist/drag_drop_import.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/dist/shortcuts_handler.js?v=5.2.2"></script>
  <script data-cfasync="false" type="text/javascript" src="js/dist/console.js?v=5.2.2"></script>

<script data-cfasync="false" type="text/javascript">
// <![CDATA[
CommonParams.setAll({common_query:"lang=en",opendb_url:"index.php?route=/database/structure&lang=en",lang:"en",server:"1",table:"",db:"",token:"562c222656407b5f2b4d7e3264497221",text_dir:"ltr",LimitChars:"50",pftext:"",confirm:true,LoginCookieValidity:"1440",session_gc_maxlifetime:"1440",logged_in:true,is_https:false,rootPath:true,arg_separator:"&",version:"5.2.2",auth_type:"config",user:"root"});
var firstDayOfCalendar = '0';
var themeImagePath = '.\/themes\/pmahomme\/img\/';
var mysqlDocTemplate = '.\/url.php\u003Furl\u003Dhttps\u00253A\u00252F\u00252Fdev.mysql.com\u00252Fdoc\u00252Frefman\u00252F8.0\u00252Fen\u00252F\u002525s.html';
var maxInputVars = 10000;

if ($.datepicker) {
  $.datepicker.regional[''].closeText = 'Done';
  $.datepicker.regional[''].prevText = 'Prev';
  $.datepicker.regional[''].nextText = 'Next';
  $.datepicker.regional[''].currentText = 'Today';
  $.datepicker.regional[''].monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];
  $.datepicker.regional[''].monthNamesShort = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ];
  $.datepicker.regional[''].dayNames = [
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ];
  $.datepicker.regional[''].dayNamesShort = [
    'Sun',
    'Mon',
    'Tue',
    'Wed',
    'Thu',
    'Fri',
    'Sat',
  ];
  $.datepicker.regional[''].dayNamesMin = [
    'Su',
    'Mo',
    'Tu',
    'We',
    'Th',
    'Fr',
    'Sa',
  ];
  $.datepicker.regional[''].weekHeader = 'Wk';
  $.datepicker.regional[''].showMonthAfterYear = false;
  $.datepicker.regional[''].yearSuffix = '';
  $.extend($.datepicker._defaults, $.datepicker.regional['']);
}

if ($.timepicker) {
  $.timepicker.regional[''].timeText = 'Time';
  $.timepicker.regional[''].hourText = 'Hour';
  $.timepicker.regional[''].minuteText = 'Minute';
  $.timepicker.regional[''].secondText = 'Second';
  $.extend($.timepicker._defaults, $.timepicker.regional['']);
}

function extendingValidatorMessages () {
  $.extend($.validator.messages, {
    required: 'This\u0020field\u0020is\u0020required',
    remote: 'Please\u0020fix\u0020this\u0020field',
    email: 'Please\u0020enter\u0020a\u0020valid\u0020email\u0020address',
    url: 'Please\u0020enter\u0020a\u0020valid\u0020URL',
    date: 'Please\u0020enter\u0020a\u0020valid\u0020date',
    dateISO: 'Please\u0020enter\u0020a\u0020valid\u0020date\u0020\u0028\u0020ISO\u0020\u0029',
    number: 'Please\u0020enter\u0020a\u0020valid\u0020number',
    creditcard: 'Please\u0020enter\u0020a\u0020valid\u0020credit\u0020card\u0020number',
    digits: 'Please\u0020enter\u0020only\u0020digits',
    equalTo: 'Please\u0020enter\u0020the\u0020same\u0020value\u0020again',
    maxlength: $.validator.format('Please\u0020enter\u0020no\u0020more\u0020than\u0020\u007B0\u007D\u0020characters'),
    minlength: $.validator.format('Please\u0020enter\u0020at\u0020least\u0020\u007B0\u007D\u0020characters'),
    rangelength: $.validator.format('Please\u0020enter\u0020a\u0020value\u0020between\u0020\u007B0\u007D\u0020and\u0020\u007B1\u007D\u0020characters\u0020long'),
    range: $.validator.format('Please\u0020enter\u0020a\u0020value\u0020between\u0020\u007B0\u007D\u0020and\u0020\u007B1\u007D'),
    max: $.validator.format('Please\u0020enter\u0020a\u0020value\u0020less\u0020than\u0020or\u0020equal\u0020to\u0020\u007B0\u007D'),
    min: $.validator.format('Please\u0020enter\u0020a\u0020value\u0020greater\u0020than\u0020or\u0020equal\u0020to\u0020\u007B0\u007D'),
    validationFunctionForDateTime: $.validator.format('Please\u0020enter\u0020a\u0020valid\u0020date\u0020or\u0020time'),
    validationFunctionForHex: $.validator.format('Please\u0020enter\u0020a\u0020valid\u0020HEX\u0020input'),
    validationFunctionForMd5: $.validator.format('This\u0020column\u0020can\u0020not\u0020contain\u0020a\u002032\u0020chars\u0020value'),
    validationFunctionForAesDesEncrypt: $.validator.format('These\u0020functions\u0020are\u0020meant\u0020to\u0020return\u0020a\u0020binary\u0020result\u003B\u0020to\u0020avoid\u0020inconsistent\u0020results\u0020you\u0020should\u0020store\u0020it\u0020in\u0020a\u0020BINARY,\u0020VARBINARY,\u0020or\u0020BLOB\u0020column.')
  });
}

ConsoleEnterExecutes=false

AJAX.scriptHandler
  .add('vendor/jquery/jquery.min.js', 0)
  .add('vendor/jquery/jquery-migrate.min.js', 0)
  .add('vendor/sprintf.js', 1)
  .add('ajax.js', 0)
  .add('keyhandler.js', 1)
  .add('vendor/jquery/jquery-ui.min.js', 0)
  .add('name-conflict-fixes.js', 1)
  .add('vendor/bootstrap/bootstrap.bundle.min.js', 1)
  .add('vendor/js.cookie.js', 1)
  .add('vendor/jquery/jquery.validate.min.js', 0)
  .add('vendor/jquery/jquery-ui-timepicker-addon.js', 0)
  .add('vendor/jquery/jquery.debounce-1.0.6.js', 0)
  .add('menu_resizer.js', 1)
  .add('cross_framing_protection.js', 0)
  .add('messages.php', 0)
  .add('config.js', 1)
  .add('doclinks.js', 1)
  .add('functions.js', 1)
  .add('navigation.js', 1)
  .add('indexes.js', 1)
  .add('common.js', 1)
  .add('page_settings.js', 1)
  .add('home.js', 1)
  .add('vendor/codemirror/lib/codemirror.js', 0)
  .add('vendor/codemirror/mode/sql/sql.js', 0)
  .add('vendor/codemirror/addon/runmode/runmode.js', 0)
  .add('vendor/codemirror/addon/hint/show-hint.js', 0)
  .add('vendor/codemirror/addon/hint/sql-hint.js', 0)
  .add('vendor/codemirror/addon/lint/lint.js', 0)
  .add('codemirror/addon/lint/sql-lint.js', 0)
  .add('vendor/tracekit.js', 1)
  .add('error_report.js', 1)
  .add('drag_drop_import.js', 1)
  .add('shortcuts_handler.js', 1)
  .add('console.js', 1)
;
$(function() {
        AJAX.fireOnload('vendor/sprintf.js');
        AJAX.fireOnload('keyhandler.js');
        AJAX.fireOnload('name-conflict-fixes.js');
      AJAX.fireOnload('vendor/bootstrap/bootstrap.bundle.min.js');
      AJAX.fireOnload('vendor/js.cookie.js');
            AJAX.fireOnload('menu_resizer.js');
          AJAX.fireOnload('config.js');
      AJAX.fireOnload('doclinks.js');
      AJAX.fireOnload('functions.js');
      AJAX.fireOnload('navigation.js');
      AJAX.fireOnload('indexes.js');
      AJAX.fireOnload('common.js');
      AJAX.fireOnload('page_settings.js');
      AJAX.fireOnload('home.js');
                    AJAX.fireOnload('vendor/tracekit.js');
      AJAX.fireOnload('error_report.js');
      AJAX.fireOnload('drag_drop_import.js');
      AJAX.fireOnload('shortcuts_handler.js');
      AJAX.fireOnload('console.js');
  });
// ]]>
</script>

  <noscript><style>html{display:block}</style></noscript>
</head>
<body>
    <div id="pma_navigation" class="d-print-none" data-config-navigation-width="240">
    <div id="pma_navigation_resizer"></div>
    <div id="pma_navigation_collapser"></div>
    <div id="pma_navigation_content">
      <div id="pma_navigation_header">

                  <div id="pmalogo">
                          <a href="index.php?lang=en">
                                      <img id="imgpmalogo" src="./themes/pmahomme/img/logo_left.png" alt="phpMyAdmin">
                                      </a>
                      </div>
        
        <div id="navipanellinks">
          <a href="index.php?route=/&lang=en" title="Home"><img src="themes/dot.gif" title="Home" alt="Home" class="icon ic_b_home"></a>

                      <a class="logout disableAjax" href="index.php?route=/logout&lang=en" title="Empty session data"><img src="themes/dot.gif" title="Empty session data" alt="Empty session data" class="icon ic_s_loggoff"></a>
          
          <a href="./doc/html/index.html" title="phpMyAdmin documentation" target="_blank" rel="noopener noreferrer"><img src="themes/dot.gif" title="phpMyAdmin documentation" alt="phpMyAdmin documentation" class="icon ic_b_docs"></a>

          <a href="./url.php?url=https%3A%2F%2Fdev.mysql.com%2Fdoc%2Frefman%2F8.0%2Fen%2Findex.html" title="MySQL Documentation" target="_blank" rel="noopener noreferrer"><img src="themes/dot.gif" title="MySQL Documentation" alt="MySQL Documentation" class="icon ic_b_sqlhelp"></a>

          <a id="pma_navigation_settings_icon" href="#" title="Navigation panel settings"><img src="themes/dot.gif" title="Navigation panel settings" alt="Navigation panel settings" class="icon ic_s_cog"></a>

          <a id="pma_navigation_reload" href="#" title="Reload navigation panel"><img src="themes/dot.gif" title="Reload navigation panel" alt="Reload navigation panel" class="icon ic_s_reload"></a>
        </div>

        
        <img src="themes/dot.gif" title="Loading…" alt="Loading…" style="visibility: hidden; display:none" class="icon ic_ajax_clock_small throbber">
      </div>
      <div id="pma_navigation_tree" class="list_container synced highlight autoexpand">

  <div class="pma_quick_warp">
    <div class="drop_list"><button title="Recent tables" class="drop_button btn">Recent</button><ul id="pma_recent_list"><li class="warp_link">
            There are no recent tables.    </li>
</ul></div>    <div class="drop_list"><button title="Favorite tables" class="drop_button btn">Favorites</button><ul id="pma_favorite_list"><li class="warp_link">
            There are no favorite tables.    </li>
</ul></div>    <div class="clearfloat"></div>
</div>


<div class="clearfloat"></div>

<ul>
  
  <!-- CONTROLS START -->
<li id="navigation_controls_outer">
    <div id="navigation_controls">
        <a href="#" id="pma_navigation_collapse" title="Collapse all"><img src="themes/dot.gif" title="Collapse all" alt="Collapse all" class="icon ic_s_collapseall"></a>
        <a href="#" id="pma_navigation_sync" title="Unlink from main panel"><img src="themes/dot.gif" title="Unlink from main panel" alt="Unlink from main panel" class="icon ic_s_link"></a>
    </div>
</li>
<!-- CONTROLS ENDS -->

</ul>



<div id='pma_navigation_tree_content'>
  <ul>
      <li class="first new_database italics">
    <div class="block">
      <i class="first"></i>
          </div>
    
          <div class="block second">
                  <a href="index.php?route=/server/databases&lang=en"><img src="themes/dot.gif" title="New" alt="New" class="icon ic_b_newdb"></a>
              </div>

              <a class="hover_show_full" href="index.php?route=/server/databases&lang=en" title="New">New</a>
          
    

    
    <div class="clearfloat"></div>



  </li>
  <li class="database">
    <div class="block">
      <i></i>
              <b></b>
        <a class="expander" href="#">
          <span class="hide paths_nav" data-apath="cm9vdA==.Y2FzaW5vX2Ri" data-vpath="cm9vdA==.Y2FzaW5vX2Ri" data-pos="0"></span>
                    <img src="themes/dot.gif" title="Expand/Collapse" alt="Expand/Collapse" class="icon ic_b_plus">
        </a>
          </div>
    
          <div class="block second">
                  <a href="index.php?route=/database/operations&db=casino_db&lang=en"><img src="themes/dot.gif" title="Database operations" alt="Database operations" class="icon ic_s_db"></a>
              </div>

              <a class="hover_show_full" href="index.php?route=/database/structure&db=casino_db&lang=en" title="Structure">casino_db</a>
          
    

    
    <div class="clearfloat"></div>



  </li>
  <li class="database">
    <div class="block">
      <i></i>
              <b></b>
        <a class="expander" href="#">
          <span class="hide paths_nav" data-apath="cm9vdA==.aW5mb3JtYXRpb25fc2NoZW1h" data-vpath="cm9vdA==.aW5mb3JtYXRpb25fc2NoZW1h" data-pos="0"></span>
                    <img src="themes/dot.gif" title="Expand/Collapse" alt="Expand/Collapse" class="icon ic_b_plus">
        </a>
          </div>
    
          <div class="block second">
                  <a href="index.php?route=/database/operations&db=information_schema&lang=en"><img src="themes/dot.gif" title="Database operations" alt="Database operations" class="icon ic_s_db"></a>
              </div>

              <a class="hover_show_full" href="index.php?route=/database/structure&db=information_schema&lang=en" title="Structure">information_schema</a>
          
    

    
    <div class="clearfloat"></div>



  </li>
  <li class="database">
    <div class="block">
      <i></i>
              <b></b>
        <a class="expander" href="#">
          <span class="hide paths_nav" data-apath="cm9vdA==.bXlzcWw=" data-vpath="cm9vdA==.bXlzcWw=" data-pos="0"></span>
                    <img src="themes/dot.gif" title="Expand/Collapse" alt="Expand/Collapse" class="icon ic_b_plus">
        </a>
          </div>
    
          <div class="block second">
                  <a href="index.php?route=/database/operations&db=mysql&lang=en"><img src="themes/dot.gif" title="Database operations" alt="Database operations" class="icon ic_s_db"></a>
              </div>

              <a class="hover_show_full" href="index.php?route=/database/structure&db=mysql&lang=en" title="Structure">mysql</a>
          
    

    
    <div class="clearfloat"></div>



  </li>
  <li class="database">
    <div class="block">
      <i></i>
              <b></b>
        <a class="expander" href="#">
          <span class="hide paths_nav" data-apath="cm9vdA==.cGVyZm9ybWFuY2Vfc2NoZW1h" data-vpath="cm9vdA==.cGVyZm9ybWFuY2Vfc2NoZW1h" data-pos="0"></span>
                    <img src="themes/dot.gif" title="Expand/Collapse" alt="Expand/Collapse" class="icon ic_b_plus">
        </a>
          </div>
    
          <div class="block second">
                  <a href="index.php?route=/database/operations&db=performance_schema&lang=en"><img src="themes/dot.gif" title="Database operations" alt="Database operations" class="icon ic_s_db"></a>
              </div>

              <a class="hover_show_full" href="index.php?route=/database/structure&db=performance_schema&lang=en" title="Structure">performance_schema</a>
          
    

    
    <div class="clearfloat"></div>



  </li>
  <li class="last database">
    <div class="block">
      <i></i>
              
        <a class="expander" href="#">
          <span class="hide paths_nav" data-apath="cm9vdA==.c3lz" data-vpath="cm9vdA==.c3lz" data-pos="0"></span>
                    <img src="themes/dot.gif" title="Expand/Collapse" alt="Expand/Collapse" class="icon ic_b_plus">
        </a>
          </div>
    
          <div class="block second">
                  <a href="index.php?route=/database/operations&db=sys&lang=en"><img src="themes/dot.gif" title="Database operations" alt="Database operations" class="icon ic_s_db"></a>
              </div>

              <a class="hover_show_full" href="index.php?route=/database/structure&db=sys&lang=en" title="Structure">sys</a>
          
    

    
    <div class="clearfloat"></div>



  </li>

  </ul>
</div>


      </div>

      <div id="pma_navi_settings_container">
                  <div id="pma_navigation_settings"><div class="page_settings"><form method="post" action="index.php&#x3F;route&#x3D;&#x25;2F&amp;server&#x3D;1&amp;lang&#x3D;en" class="config-form disableAjax">
  <input type="hidden" name="tab_hash" value="">
      <input type="hidden" name="check_page_refresh" id="check_page_refresh" value="">
    <input type="hidden" name="lang" value="en"><input type="hidden" name="token" value="562c222656407b5f2b4d7e3264497221">
  <input type="hidden" name="submit_save" value="Navi">

  <ul class="nav nav-tabs" id="configFormDisplayTab" role="tablist">
          <li class="nav-item" role="presentation">
        <a class="nav-link active" id="Navi_panel-tab" href="#Navi_panel" data-bs-toggle="tab" role="tab" aria-controls="Navi_panel" aria-selected="true">Navigation panel</a>
      </li>
          <li class="nav-item" role="presentation">
        <a class="nav-link" id="Navi_tree-tab" href="#Navi_tree" data-bs-toggle="tab" role="tab" aria-controls="Navi_tree" aria-selected="false">Navigation tree</a>
      </li>
          <li class="nav-item" role="presentation">
        <a class="nav-link" id="Navi_servers-tab" href="#Navi_servers" data-bs-toggle="tab" role="tab" aria-controls="Navi_servers" aria-selected="false">Servers</a>
      </li>
          <li class="nav-item" role="presentation">
        <a class="nav-link" id="Navi_databases-tab" href="#Navi_databases" data-bs-toggle="tab" role="tab" aria-controls="Navi_databases" aria-selected="false">Databases</a>
      </li>
          <li class="nav-item" role="presentation">
        <a class="nav-link" id="Navi_tables-tab" href="#Navi_tables" data-bs-toggle="tab" role="tab" aria-controls="Navi_tables" aria-selected="false">Tables</a>
      </li>
      </ul>
  <div class="tab-content">
          <div class="tab-pane fade show active" id="Navi_panel" role="tabpanel" aria-labelledby="Navi_panel-tab">
        <div class="card border-top-0">
          <div class="card-body">
            <h5 class="card-title visually-hidden">Navigation panel</h5>
                          <h6 class="card-subtitle mb-2 text-muted">Customize appearance of the navigation panel.</h6>
            
            <fieldset class="optbox">
              <legend>Navigation panel</legend>

                            
              <table class="table table-borderless">
                <tr>
  <th>
    <label for="ShowDatabasesNavigationAsTree">Show databases navigation as tree</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_ShowDatabasesNavigationAsTree" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>In the navigation panel, replaces the database tree with a selector</small>
      </th>

  <td>
          <span class="checkbox">
        <input type="checkbox" name="ShowDatabasesNavigationAsTree" id="ShowDatabasesNavigationAsTree" checked>
      </span>
    
    
    
          <a class="restore-default hide" href="#ShowDatabasesNavigationAsTree" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationLinkWithMainPanel">Link with main panel</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationLinkWithMainPanel" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>Link with main panel by highlighting the current database or table.</small>
      </th>

  <td>
          <span class="checkbox">
        <input type="checkbox" name="NavigationLinkWithMainPanel" id="NavigationLinkWithMainPanel" checked>
      </span>
    
    
    
          <a class="restore-default hide" href="#NavigationLinkWithMainPanel" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationDisplayLogo">Display logo</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationDisplayLogo" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>Show logo in navigation panel.</small>
      </th>

  <td>
          <span class="checkbox">
        <input type="checkbox" name="NavigationDisplayLogo" id="NavigationDisplayLogo" checked>
      </span>
    
    
    
          <a class="restore-default hide" href="#NavigationDisplayLogo" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationLogoLink">Logo link URL</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationLogoLink" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>URL where logo in the navigation panel will point to.</small>
      </th>

  <td>
          <input type="text" name="NavigationLogoLink" id="NavigationLogoLink" value="index.php" class="w-75">
    
    
    
          <a class="restore-default hide" href="#NavigationLogoLink" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationLogoLinkWindow">Logo link target</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationLogoLinkWindow" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>Open the linked page in the main window (<code>main</code>) or in a new one (<code>new</code>).</small>
      </th>

  <td>
          <select name="NavigationLogoLinkWindow" id="NavigationLogoLinkWindow" class="w-75">
                            <option value="main" selected>main</option>
                            <option value="new">new</option>
              </select>
    
    
    
          <a class="restore-default hide" href="#NavigationLogoLinkWindow" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationTreePointerEnable">Enable highlighting</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationTreePointerEnable" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>Highlight server under the mouse cursor.</small>
      </th>

  <td>
          <span class="checkbox">
        <input type="checkbox" name="NavigationTreePointerEnable" id="NavigationTreePointerEnable" checked>
      </span>
    
    
    
          <a class="restore-default hide" href="#NavigationTreePointerEnable" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="FirstLevelNavigationItems">Maximum items on first level</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_FirstLevelNavigationItems" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>The number of items that can be displayed on each page on the first level of the navigation tree.</small>
      </th>

  <td>
          <input type="number" name="FirstLevelNavigationItems" id="FirstLevelNavigationItems" value="100" class="">
    
    
    
          <a class="restore-default hide" href="#FirstLevelNavigationItems" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationTreeDisplayItemFilterMinimum">Minimum number of items to display the filter box</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationTreeDisplayItemFilterMinimum" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>Defines the minimum number of items (tables, views, routines and events) to display a filter box.</small>
      </th>

  <td>
          <input type="number" name="NavigationTreeDisplayItemFilterMinimum" id="NavigationTreeDisplayItemFilterMinimum" value="30" class="">
    
    
    
          <a class="restore-default hide" href="#NavigationTreeDisplayItemFilterMinimum" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NumRecentTables">Recently used tables</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NumRecentTables" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>Maximum number of recently used tables; set 0 to disable.</small>
      </th>

  <td>
          <input type="number" name="NumRecentTables" id="NumRecentTables" value="10" class="">
    
    
    
          <a class="restore-default hide" href="#NumRecentTables" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NumFavoriteTables">Favorite tables</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NumFavoriteTables" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>Maximum number of favorite tables; set 0 to disable.</small>
      </th>

  <td>
          <input type="number" name="NumFavoriteTables" id="NumFavoriteTables" value="10" class="">
    
    
    
          <a class="restore-default hide" href="#NumFavoriteTables" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationWidth">Navigation panel width</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationWidth" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>Set to 0 to collapse navigation panel.</small>
      </th>

  <td>
          <input type="number" name="NavigationWidth" id="NavigationWidth" value="240" class="">
    
    
    
          <a class="restore-default hide" href="#NavigationWidth" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>

              </table>
            </fieldset>
          </div>

                  </div>
      </div>
          <div class="tab-pane fade" id="Navi_tree" role="tabpanel" aria-labelledby="Navi_tree-tab">
        <div class="card border-top-0">
          <div class="card-body">
            <h5 class="card-title visually-hidden">Navigation tree</h5>
                          <h6 class="card-subtitle mb-2 text-muted">Customize the navigation tree.</h6>
            
            <fieldset class="optbox">
              <legend>Navigation tree</legend>

                            
              <table class="table table-borderless">
                <tr>
  <th>
    <label for="MaxNavigationItems">Maximum items in branch</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_MaxNavigationItems" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>The number of items that can be displayed on each page of the navigation tree.</small>
      </th>

  <td>
          <input type="number" name="MaxNavigationItems" id="MaxNavigationItems" value="50" class="">
    
    
    
          <a class="restore-default hide" href="#MaxNavigationItems" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationTreeEnableGrouping">Group items in the tree</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationTreeEnableGrouping" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>Group items in the navigation tree (determined by the separator defined in the Databases and Tables tabs above).</small>
      </th>

  <td>
          <span class="checkbox">
        <input type="checkbox" name="NavigationTreeEnableGrouping" id="NavigationTreeEnableGrouping" checked>
      </span>
    
    
    
          <a class="restore-default hide" href="#NavigationTreeEnableGrouping" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationTreeEnableExpansion">Enable navigation tree expansion</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationTreeEnableExpansion" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>Whether to offer the possibility of tree expansion in the navigation panel.</small>
      </th>

  <td>
          <span class="checkbox">
        <input type="checkbox" name="NavigationTreeEnableExpansion" id="NavigationTreeEnableExpansion" checked>
      </span>
    
    
    
          <a class="restore-default hide" href="#NavigationTreeEnableExpansion" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationTreeShowTables">Show tables in tree</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationTreeShowTables" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>Whether to show tables under database in the navigation tree</small>
      </th>

  <td>
          <span class="checkbox">
        <input type="checkbox" name="NavigationTreeShowTables" id="NavigationTreeShowTables" checked>
      </span>
    
    
    
          <a class="restore-default hide" href="#NavigationTreeShowTables" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationTreeShowViews">Show views in tree</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationTreeShowViews" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>Whether to show views under database in the navigation tree</small>
      </th>

  <td>
          <span class="checkbox">
        <input type="checkbox" name="NavigationTreeShowViews" id="NavigationTreeShowViews" checked>
      </span>
    
    
    
          <a class="restore-default hide" href="#NavigationTreeShowViews" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationTreeShowFunctions">Show functions in tree</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationTreeShowFunctions" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>Whether to show functions under database in the navigation tree</small>
      </th>

  <td>
          <span class="checkbox">
        <input type="checkbox" name="NavigationTreeShowFunctions" id="NavigationTreeShowFunctions" checked>
      </span>
    
    
    
          <a class="restore-default hide" href="#NavigationTreeShowFunctions" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationTreeShowProcedures">Show procedures in tree</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationTreeShowProcedures" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>Whether to show procedures under database in the navigation tree</small>
      </th>

  <td>
          <span class="checkbox">
        <input type="checkbox" name="NavigationTreeShowProcedures" id="NavigationTreeShowProcedures" checked>
      </span>
    
    
    
          <a class="restore-default hide" href="#NavigationTreeShowProcedures" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationTreeShowEvents">Show events in tree</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationTreeShowEvents" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>Whether to show events under database in the navigation tree</small>
      </th>

  <td>
          <span class="checkbox">
        <input type="checkbox" name="NavigationTreeShowEvents" id="NavigationTreeShowEvents" checked>
      </span>
    
    
    
          <a class="restore-default hide" href="#NavigationTreeShowEvents" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationTreeAutoexpandSingleDb">Expand single database</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationTreeAutoexpandSingleDb" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>Whether to expand single database in the navigation tree automatically.</small>
      </th>

  <td>
          <span class="checkbox">
        <input type="checkbox" name="NavigationTreeAutoexpandSingleDb" id="NavigationTreeAutoexpandSingleDb" checked>
      </span>
    
    
    
          <a class="restore-default hide" href="#NavigationTreeAutoexpandSingleDb" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>

              </table>
            </fieldset>
          </div>

                  </div>
      </div>
          <div class="tab-pane fade" id="Navi_servers" role="tabpanel" aria-labelledby="Navi_servers-tab">
        <div class="card border-top-0">
          <div class="card-body">
            <h5 class="card-title visually-hidden">Servers</h5>
                          <h6 class="card-subtitle mb-2 text-muted">Servers display options.</h6>
            
            <fieldset class="optbox">
              <legend>Servers</legend>

                            
              <table class="table table-borderless">
                <tr>
  <th>
    <label for="NavigationDisplayServers">Display servers selection</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationDisplayServers" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>Display server choice at the top of the navigation panel.</small>
      </th>

  <td>
          <span class="checkbox">
        <input type="checkbox" name="NavigationDisplayServers" id="NavigationDisplayServers" checked>
      </span>
    
    
    
          <a class="restore-default hide" href="#NavigationDisplayServers" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="DisplayServersList">Display servers as a list</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_DisplayServersList" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>Show server listing as a list instead of a drop down.</small>
      </th>

  <td>
          <span class="checkbox">
        <input type="checkbox" name="DisplayServersList" id="DisplayServersList">
      </span>
    
    
    
          <a class="restore-default hide" href="#DisplayServersList" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>

              </table>
            </fieldset>
          </div>

                  </div>
      </div>
          <div class="tab-pane fade" id="Navi_databases" role="tabpanel" aria-labelledby="Navi_databases-tab">
        <div class="card border-top-0">
          <div class="card-body">
            <h5 class="card-title visually-hidden">Databases</h5>
                          <h6 class="card-subtitle mb-2 text-muted">Databases display options.</h6>
            
            <fieldset class="optbox">
              <legend>Databases</legend>

                            
              <table class="table table-borderless">
                <tr>
  <th>
    <label for="NavigationTreeDisplayDbFilterMinimum">Minimum number of databases to display the database filter box</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationTreeDisplayDbFilterMinimum" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
      </th>

  <td>
          <input type="number" name="NavigationTreeDisplayDbFilterMinimum" id="NavigationTreeDisplayDbFilterMinimum" value="30" class="">
    
    
    
          <a class="restore-default hide" href="#NavigationTreeDisplayDbFilterMinimum" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationTreeDbSeparator">Database tree separator</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationTreeDbSeparator" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>String that separates databases into different tree levels.</small>
      </th>

  <td>
                <input type="text" size="25" name="NavigationTreeDbSeparator" id="NavigationTreeDbSeparator" value="_" class="">
    
    
    
          <a class="restore-default hide" href="#NavigationTreeDbSeparator" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>

              </table>
            </fieldset>
          </div>

                  </div>
      </div>
          <div class="tab-pane fade" id="Navi_tables" role="tabpanel" aria-labelledby="Navi_tables-tab">
        <div class="card border-top-0">
          <div class="card-body">
            <h5 class="card-title visually-hidden">Tables</h5>
                          <h6 class="card-subtitle mb-2 text-muted">Tables display options.</h6>
            
            <fieldset class="optbox">
              <legend>Tables</legend>

                            
              <table class="table table-borderless">
                <tr>
  <th>
    <label for="NavigationTreeDefaultTabTable">Target for quick access icon</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationTreeDefaultTabTable" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
      </th>

  <td>
          <select name="NavigationTreeDefaultTabTable" id="NavigationTreeDefaultTabTable" class="w-75">
                            <option value="structure" selected>Structure</option>
                            <option value="sql">SQL</option>
                            <option value="search">Search</option>
                            <option value="insert">Insert</option>
                            <option value="browse">Browse</option>
              </select>
    
    
    
          <a class="restore-default hide" href="#NavigationTreeDefaultTabTable" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationTreeDefaultTabTable2">Target for second quick access icon</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationTreeDefaultTabTable2" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
      </th>

  <td>
          <select name="NavigationTreeDefaultTabTable2" id="NavigationTreeDefaultTabTable2" class="w-75">
                            <option value="" selected></option>
                            <option value="structure">Structure</option>
                            <option value="sql">SQL</option>
                            <option value="search">Search</option>
                            <option value="insert">Insert</option>
                            <option value="browse">Browse</option>
              </select>
    
    
    
          <a class="restore-default hide" href="#NavigationTreeDefaultTabTable2" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationTreeTableSeparator">Table tree separator</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationTreeTableSeparator" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
          <small>String that separates tables into different tree levels.</small>
      </th>

  <td>
                <input type="text" size="25" name="NavigationTreeTableSeparator" id="NavigationTreeTableSeparator" value="__" class="">
    
    
    
          <a class="restore-default hide" href="#NavigationTreeTableSeparator" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>
<tr>
  <th>
    <label for="NavigationTreeTableLevel">Maximum table tree depth</label>

          <span class="doc">
        <a href="./doc/html/config.html#cfg_NavigationTreeTableLevel" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
      </span>
    
    
      </th>

  <td>
          <input type="number" name="NavigationTreeTableLevel" id="NavigationTreeTableLevel" value="1" class="">
    
    
    
          <a class="restore-default hide" href="#NavigationTreeTableLevel" title="Restore default value"><img src="themes/dot.gif" title="Restore default value" alt="Restore default value" class="icon ic_s_reload"></a>
    
          </td>

  </tr>

              </table>
            </fieldset>
          </div>

                  </div>
      </div>
      </div>
</form>

<script type="text/javascript">
  if (typeof configInlineParams === 'undefined' || !Array.isArray(configInlineParams)) {
    configInlineParams = [];
  }
  configInlineParams.push(function () {
    registerFieldValidator('FirstLevelNavigationItems', 'validatePositiveNumber', true);
registerFieldValidator('NavigationTreeDisplayItemFilterMinimum', 'validatePositiveNumber', true);
registerFieldValidator('NumRecentTables', 'validateNonNegativeNumber', true);
registerFieldValidator('NumFavoriteTables', 'validateNonNegativeNumber', true);
registerFieldValidator('NavigationWidth', 'validateNonNegativeNumber', true);
registerFieldValidator('MaxNavigationItems', 'validatePositiveNumber', true);
registerFieldValidator('NavigationTreeTableLevel', 'validatePositiveNumber', true);

    $.extend(Messages, {
      'error_nan_p': 'Not\u0020a\u0020positive\u0020number\u0021',
      'error_nan_nneg': 'Not\u0020a\u0020non\u002Dnegative\u0020number\u0021',
      'error_incorrect_port': 'Not\u0020a\u0020valid\u0020port\u0020number\u0021',
      'error_invalid_value': 'Incorrect\u0020value\u0021',
      'error_value_lte': 'Value\u0020must\u0020be\u0020less\u0020than\u0020or\u0020equal\u0020to\u0020\u0025s\u0021',
    });

    $.extend(defaultValues, {
      'ShowDatabasesNavigationAsTree': true,
      'NavigationLinkWithMainPanel': true,
      'NavigationDisplayLogo': true,
      'NavigationLogoLink': 'index.php',
      'NavigationLogoLinkWindow': ['main'],
      'NavigationTreePointerEnable': true,
      'FirstLevelNavigationItems': '100',
      'NavigationTreeDisplayItemFilterMinimum': '30',
      'NumRecentTables': '10',
      'NumFavoriteTables': '10',
      'NavigationWidth': '240',
      'MaxNavigationItems': '50',
      'NavigationTreeEnableGrouping': true,
      'NavigationTreeEnableExpansion': true,
      'NavigationTreeShowTables': true,
      'NavigationTreeShowViews': true,
      'NavigationTreeShowFunctions': true,
      'NavigationTreeShowProcedures': true,
      'NavigationTreeShowEvents': true,
      'NavigationTreeAutoexpandSingleDb': true,
      'NavigationDisplayServers': true,
      'DisplayServersList': false,
      'NavigationTreeDisplayDbFilterMinimum': '30',
      'NavigationTreeDbSeparator': '_',
      'NavigationTreeDefaultTabTable': ['structure'],
      'NavigationTreeDefaultTabTable2': [''],
      'NavigationTreeTableSeparator': '__',
      'NavigationTreeTableLevel': '1'
    });
  });
  if (typeof configScriptLoaded !== 'undefined' && configInlineParams) {
    loadInlineConfig();
  }
</script>
</div></div>
              </div>
    </div>

          <div class="pma_drop_handler">
        Drop files here      </div>
      <div class="pma_sql_import_status">
        <h2>
          SQL upload          ( <span class="pma_import_count">0</span> )
          <span class="close">x</span>
          <span class="minimize">-</span>
        </h2>
        <div></div>
      </div>
      </div>
  <div class="modal fade" id="unhideNavItemModal" tabindex="-1" aria-labelledby="unhideNavItemModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="unhideNavItemModalLabel">Show hidden navigation tree items.</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body"></div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

  <div class="modal fade" id="createViewModal" tabindex="-1" aria-labelledby="createViewModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" id="createViewModalDialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="createViewModalLabel">Create view</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body"></div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" id="createViewModalGoButton">Go</button>
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>


  
  <div id="prefs_autoload" class="alert alert-primary d-print-none hide" role="alert">
    <form action="index.php?route=/preferences/manage&lang=en" method="post" class="disableAjax">
        <input type="hidden" name="lang" value="en"><input type="hidden" name="token" value="562c222656407b5f2b4d7e3264497221">
        <input type="hidden" name="json" value="">
        <input type="hidden" name="submit_import" value="1">
        <input type="hidden" name="return_url" value="index.php?">
        Your browser has phpMyAdmin configuration for this domain. Would you like to import it for current session?        <br>
        <a href="#yes">Yes</a>
        / <a href="#no">No</a>
        / <a href="#delete">Delete settings</a>
    </form>
</div>


  
      <noscript>
      <div class="alert alert-danger" role="alert">
  <img src="themes/dot.gif" title="" alt="" class="icon ic_s_error"> Javascript must be enabled past this point!
</div>

    </noscript>
  
      <div id="floating_menubar" class="d-print-none"></div>
<nav id="server-breadcrumb" aria-label="breadcrumb">
  <ol class="breadcrumb breadcrumb-navbar">
    <li class="breadcrumb-item">
      <img src="themes/dot.gif" title="" alt="" class="icon ic_s_host">
      <a href="index.php?route=/&lang=en" data-raw-text="mysql:3306" draggable="false">
        Server:        mysql:3306
      </a>
    </li>

      </ol>
</nav>
<div id="topmenucontainer" class="menucontainer">
  <nav class="navbar navbar-expand-lg navbar-light bg-light">
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-label="Toggle navigation" aria-controls="navbarNav" aria-expanded="false">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
      <ul id="topmenu" class="navbar-nav">
                  <li class="nav-item">
            <a class="nav-link text-nowrap disableAjax" href="index.php?route=/server/databases&lang=en">
              <img src="themes/dot.gif" title="Databases" alt="Databases" class="icon ic_s_db">&nbsp;Databases
                          </a>
          </li>
                  <li class="nav-item">
            <a class="nav-link text-nowrap disableAjax" href="index.php?route=/server/sql&lang=en">
              <img src="themes/dot.gif" title="SQL" alt="SQL" class="icon ic_b_sql">&nbsp;SQL
                          </a>
          </li>
                  <li class="nav-item">
            <a class="nav-link text-nowrap disableAjax" href="index.php?route=/server/status&lang=en">
              <img src="themes/dot.gif" title="Status" alt="Status" class="icon ic_s_status">&nbsp;Status
                          </a>
          </li>
                  <li class="nav-item">
            <a class="nav-link text-nowrap disableAjax" href="index.php?route=/server/privileges&viewing_mode=server&lang=en">
              <img src="themes/dot.gif" title="User accounts" alt="User accounts" class="icon ic_s_rights">&nbsp;User accounts
                          </a>
          </li>
                  <li class="nav-item">
            <a class="nav-link text-nowrap disableAjax" href="index.php?route=/server/export&lang=en">
              <img src="themes/dot.gif" title="Export" alt="Export" class="icon ic_b_export">&nbsp;Export
                          </a>
          </li>
                  <li class="nav-item">
            <a class="nav-link text-nowrap disableAjax" href="index.php?route=/server/import&lang=en">
              <img src="themes/dot.gif" title="Import" alt="Import" class="icon ic_b_import">&nbsp;Import
                          </a>
          </li>
                  <li class="nav-item">
            <a class="nav-link text-nowrap disableAjax" href="index.php?route=/preferences/manage&lang=en">
              <img src="themes/dot.gif" title="Settings" alt="Settings" class="icon ic_b_tblops">&nbsp;Settings
                          </a>
          </li>
                  <li class="nav-item">
            <a class="nav-link text-nowrap disableAjax" href="index.php?route=/server/binlog&lang=en">
              <img src="themes/dot.gif" title="Binary log" alt="Binary log" class="icon ic_s_tbl">&nbsp;Binary log
                          </a>
          </li>
                  <li class="nav-item">
            <a class="nav-link text-nowrap disableAjax" href="index.php?route=/server/replication&lang=en">
              <img src="themes/dot.gif" title="Replication" alt="Replication" class="icon ic_s_replication">&nbsp;Replication
                          </a>
          </li>
                  <li class="nav-item">
            <a class="nav-link text-nowrap disableAjax" href="index.php?route=/server/variables&lang=en">
              <img src="themes/dot.gif" title="Variables" alt="Variables" class="icon ic_s_vars">&nbsp;Variables
                          </a>
          </li>
                  <li class="nav-item">
            <a class="nav-link text-nowrap disableAjax" href="index.php?route=/server/collations&lang=en">
              <img src="themes/dot.gif" title="Charsets" alt="Charsets" class="icon ic_s_asci">&nbsp;Charsets
                          </a>
          </li>
                  <li class="nav-item">
            <a class="nav-link text-nowrap disableAjax" href="index.php?route=/server/engines&lang=en">
              <img src="themes/dot.gif" title="Engines" alt="Engines" class="icon ic_b_engine">&nbsp;Engines
                          </a>
          </li>
                  <li class="nav-item">
            <a class="nav-link text-nowrap disableAjax" href="index.php?route=/server/plugins&lang=en">
              <img src="themes/dot.gif" title="Plugins" alt="Plugins" class="icon ic_b_plugin">&nbsp;Plugins
                          </a>
          </li>
              </ul>
    </div>
  </nav>
</div>

    <span id="page_nav_icons" class="d-print-none">
      <span id="lock_page_icon"></span>
      <span id="page_settings_icon">
        <img src="themes/dot.gif" title="Page-related settings" alt="Page-related settings" class="icon ic_s_cog">
      </span>
      <a id="goto_pagetop" href="#"><img src="themes/dot.gif" title="Click on the bar to scroll to top of page" alt="Click on the bar to scroll to top of page" class="icon ic_s_top"></a>
    </span>
  
  <div id="pma_console_container" class="d-print-none">
    <div id="pma_console">
                <div class="toolbar collapsed">
                    <div class="switch_button console_switch">
            <img src="themes/dot.gif" title="SQL Query Console" alt="SQL Query Console" class="icon ic_console">
            <span>Console</span>
        </div>
                            <div class="button clear">
            
            <span>Clear</span>
        </div>
                            <div class="button history">
            
            <span>History</span>
        </div>
                            <div class="button options">
            
            <span>Options</span>
        </div>
                                        <div class="button debug hide">
            
            <span>Debug SQL</span>
        </div>
            </div>
                <div class="content">
            <div class="console_message_container">
                <div class="message welcome">
                    <span id="instructions-0">
                        Press Ctrl+Enter to execute query                    </span>
                    <span class="hide" id="instructions-1">
                        Press Enter to execute query                    </span>
                </div>
                            </div><!-- console_message_container -->
            <div class="query_input">
                <span class="console_query_input"></span>
            </div>
        </div><!-- message end -->
                <div class="mid_layer"></div>
                <div class="card" id="debug_console">
            <div class="toolbar ">
                    <div class="button order order_asc">
            
            <span>ascending</span>
        </div>
                            <div class="button order order_desc">
            
            <span>descending</span>
        </div>
                            <div class="text">
            
            <span>Order:</span>
        </div>
                            <div class="switch_button">
            
            <span>Debug SQL</span>
        </div>
                            <div class="button order_by sort_count">
            
            <span>Count</span>
        </div>
                            <div class="button order_by sort_exec">
            
            <span>Execution order</span>
        </div>
                            <div class="button order_by sort_time">
            
            <span>Time taken</span>
        </div>
                            <div class="text">
            
            <span>Order by:</span>
        </div>
                            <div class="button group_queries">
            
            <span>Group queries</span>
        </div>
                            <div class="button ungroup_queries">
            
            <span>Ungroup queries</span>
        </div>
            </div>
            <div class="content debug">
                <div class="message welcome"></div>
                <div class="debugLog"></div>
            </div> <!-- Content -->
            <div class="templates">
                <div class="debug_query action_content">
                    <span class="action collapse">
            Collapse
                    </span>
                            <span class="action expand">
            Expand
                    </span>
                            <span class="action dbg_show_trace">
            Show trace
                    </span>
                            <span class="action dbg_hide_trace">
            Hide trace
                    </span>
                            <span class="text count hide">
            Count
                            : <span></span>
                    </span>
                            <span class="text time">
            Time taken
                            : <span></span>
                    </span>
            </div>
            </div> <!-- Template -->
        </div> <!-- Debug SQL card -->
                        <div class="card" id="pma_console_options">
            <div class="toolbar ">
                    <div class="switch_button">
            
            <span>Options</span>
        </div>
                            <div class="button default">
            
            <span>Set default</span>
        </div>
            </div>
            <div class="content">
                <label>
                    <input type="checkbox" name="always_expand">Always expand query messages                </label>
                <br>
                <label>
                    <input type="checkbox" name="start_history">Show query history at start                </label>
                <br>
                <label>
                    <input type="checkbox" name="current_query">Show current browsing query                </label>
                <br>
                <label>
                    <input type="checkbox" name="enter_executes">
                        Execute queries on Enter and insert new line with Shift+Enter. To make this permanent, view settings.                </label>
                <br>
                <label>
                    <input type="checkbox" name="dark_theme">Switch to dark theme                </label>
                <br>
            </div>
        </div> <!-- Options card -->
        <div class="templates">
                        <div class="query_actions">
                    <span class="action collapse">
            Collapse
                    </span>
                            <span class="action expand">
            Expand
                    </span>
                            <span class="action requery">
            Requery
                    </span>
                            <span class="action edit">
            Edit
                    </span>
                            <span class="action explain">
            Explain
                    </span>
                            <span class="action profiling">
            Profiling
                    </span>
                            <span class="">
            
                    </span>
                            <span class="text failed">
            Query failed
                    </span>
                            <span class="text targetdb">
            Database
                            : <span></span>
                    </span>
                            <span class="text query_time">
            Queried time
                            : <span></span>
                    </span>
            </div>
        </div>
    </div> <!-- #console end -->
</div> <!-- #console_container end -->


  <div id="page_content">
    

    <div class="modal fade" id="previewSqlModal" tabindex="-1" aria-labelledby="previewSqlModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="previewSqlModalLabel">Loading</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body"></div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

    <div class="modal fade" id="enumEditorModal" tabindex="-1" aria-labelledby="enumEditorModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="enumEditorModalLabel">ENUM/SET editor</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body"></div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" id="enumEditorGoButton" data-bs-dismiss="modal">Go</button>
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

    <div class="modal fade" id="createViewModal" tabindex="-1" aria-labelledby="createViewModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" id="createViewModalDialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="createViewModalLabel">Create view</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body"></div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" id="createViewModalGoButton">Go</button>
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>





<div id="maincontainer">
  
  <div class="container-fluid">
    <div class="row mb-3">
      <div class="col-lg-7 col-12">
                  
            <div class="card mt-4">
              <div class="card-header">
                General settings              </div>
              <ul class="list-group list-group-flush">
                
                                  
                  <li id="li_select_mysql_collation" class="list-group-item">
                    <form method="post" action="index.php?route=/collation-connection&lang=en" class="row row-cols-lg-auto align-items-center disableAjax">
                      <input type="hidden" name="lang" value="en"><input type="hidden" name="token" value="562c222656407b5f2b4d7e3264497221">
                      <div class="col-12">
                        <label for="collationConnectionSelect" class="col-form-label">
                          <img src="themes/dot.gif" title="" alt="" class="icon ic_s_asci">
                          Server connection collation:                          <a href="./url.php?url=https%3A%2F%2Fdev.mysql.com%2Fdoc%2Frefman%2F8.0%2Fen%2Fcharset-connection.html" target="mysql_doc"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
                        </label>
                      </div>
                                            <div class="col-12">
                        <select lang="en" dir="ltr" name="collation_connection" id="collationConnectionSelect" class="form-select autosubmit">
                          <option value="">Collation</option>
                          <option value=""></option>
                                                      <optgroup label="armscii8" title="ARMSCII-8 Armenian">
                                                              <option value="armscii8_bin" title="Armenian, binary">armscii8_bin</option>
                                                              <option value="armscii8_general_ci" title="Armenian, case-insensitive">armscii8_general_ci</option>
                                                          </optgroup>
                                                      <optgroup label="ascii" title="US ASCII">
                                                              <option value="ascii_bin" title="West European, binary">ascii_bin</option>
                                                              <option value="ascii_general_ci" title="West European, case-insensitive">ascii_general_ci</option>
                                                          </optgroup>
                                                      <optgroup label="big5" title="Big5 Traditional Chinese">
                                                              <option value="big5_bin" title="Traditional Chinese, binary">big5_bin</option>
                                                              <option value="big5_chinese_ci" title="Traditional Chinese, case-insensitive">big5_chinese_ci</option>
                                                          </optgroup>
                                                      <optgroup label="binary" title="Binary pseudo charset">
                                                              <option value="binary" title="Binary">binary</option>
                                                          </optgroup>
                                                      <optgroup label="cp1250" title="Windows Central European">
                                                              <option value="cp1250_bin" title="Central European, binary">cp1250_bin</option>
                                                              <option value="cp1250_croatian_ci" title="Croatian, case-insensitive">cp1250_croatian_ci</option>
                                                              <option value="cp1250_czech_cs" title="Czech, case-sensitive">cp1250_czech_cs</option>
                                                              <option value="cp1250_general_ci" title="Central European, case-insensitive">cp1250_general_ci</option>
                                                              <option value="cp1250_polish_ci" title="Polish, case-insensitive">cp1250_polish_ci</option>
                                                          </optgroup>
                                                      <optgroup label="cp1251" title="Windows Cyrillic">
                                                              <option value="cp1251_bin" title="Cyrillic, binary">cp1251_bin</option>
                                                              <option value="cp1251_bulgarian_ci" title="Bulgarian, case-insensitive">cp1251_bulgarian_ci</option>
                                                              <option value="cp1251_general_ci" title="Cyrillic, case-insensitive">cp1251_general_ci</option>
                                                              <option value="cp1251_general_cs" title="Cyrillic, case-sensitive">cp1251_general_cs</option>
                                                              <option value="cp1251_ukrainian_ci" title="Ukrainian, case-insensitive">cp1251_ukrainian_ci</option>
                                                          </optgroup>
                                                      <optgroup label="cp1256" title="Windows Arabic">
                                                              <option value="cp1256_bin" title="Arabic, binary">cp1256_bin</option>
                                                              <option value="cp1256_general_ci" title="Arabic, case-insensitive">cp1256_general_ci</option>
                                                          </optgroup>
                                                      <optgroup label="cp1257" title="Windows Baltic">
                                                              <option value="cp1257_bin" title="Baltic, binary">cp1257_bin</option>
                                                              <option value="cp1257_general_ci" title="Baltic, case-insensitive">cp1257_general_ci</option>
                                                              <option value="cp1257_lithuanian_ci" title="Lithuanian, case-insensitive">cp1257_lithuanian_ci</option>
                                                          </optgroup>
                                                      <optgroup label="cp850" title="DOS West European">
                                                              <option value="cp850_bin" title="West European, binary">cp850_bin</option>
                                                              <option value="cp850_general_ci" title="West European, case-insensitive">cp850_general_ci</option>
                                                          </optgroup>
                                                      <optgroup label="cp852" title="DOS Central European">
                                                              <option value="cp852_bin" title="Central European, binary">cp852_bin</option>
                                                              <option value="cp852_general_ci" title="Central European, case-insensitive">cp852_general_ci</option>
                                                          </optgroup>
                                                      <optgroup label="cp866" title="DOS Russian">
                                                              <option value="cp866_bin" title="Russian, binary">cp866_bin</option>
                                                              <option value="cp866_general_ci" title="Russian, case-insensitive">cp866_general_ci</option>
                                                          </optgroup>
                                                      <optgroup label="cp932" title="SJIS for Windows Japanese">
                                                              <option value="cp932_bin" title="Japanese, binary">cp932_bin</option>
                                                              <option value="cp932_japanese_ci" title="Japanese, case-insensitive">cp932_japanese_ci</option>
                                                          </optgroup>
                                                      <optgroup label="dec8" title="DEC West European">
                                                              <option value="dec8_bin" title="West European, binary">dec8_bin</option>
                                                              <option value="dec8_swedish_ci" title="Swedish, case-insensitive">dec8_swedish_ci</option>
                                                          </optgroup>
                                                      <optgroup label="eucjpms" title="UJIS for Windows Japanese">
                                                              <option value="eucjpms_bin" title="Japanese, binary">eucjpms_bin</option>
                                                              <option value="eucjpms_japanese_ci" title="Japanese, case-insensitive">eucjpms_japanese_ci</option>
                                                          </optgroup>
                                                      <optgroup label="euckr" title="EUC-KR Korean">
                                                              <option value="euckr_bin" title="Korean, binary">euckr_bin</option>
                                                              <option value="euckr_korean_ci" title="Korean, case-insensitive">euckr_korean_ci</option>
                                                          </optgroup>
                                                      <optgroup label="gb18030" title="China National Standard GB18030">
                                                              <option value="gb18030_bin" title="Chinese, binary">gb18030_bin</option>
                                                              <option value="gb18030_chinese_ci" title="Chinese, case-insensitive">gb18030_chinese_ci</option>
                                                              <option value="gb18030_unicode_520_ci" title="Chinese (UCA 5.2.0), case-insensitive">gb18030_unicode_520_ci</option>
                                                          </optgroup>
                                                      <optgroup label="gb2312" title="GB2312 Simplified Chinese">
                                                              <option value="gb2312_bin" title="Simplified Chinese, binary">gb2312_bin</option>
                                                              <option value="gb2312_chinese_ci" title="Simplified Chinese, case-insensitive">gb2312_chinese_ci</option>
                                                          </optgroup>
                                                      <optgroup label="gbk" title="GBK Simplified Chinese">
                                                              <option value="gbk_bin" title="Simplified Chinese, binary">gbk_bin</option>
                                                              <option value="gbk_chinese_ci" title="Simplified Chinese, case-insensitive">gbk_chinese_ci</option>
                                                          </optgroup>
                                                      <optgroup label="geostd8" title="GEOSTD8 Georgian">
                                                              <option value="geostd8_bin" title="Georgian, binary">geostd8_bin</option>
                                                              <option value="geostd8_general_ci" title="Georgian, case-insensitive">geostd8_general_ci</option>
                                                          </optgroup>
                                                      <optgroup label="greek" title="ISO 8859-7 Greek">
                                                              <option value="greek_bin" title="Greek, binary">greek_bin</option>
                                                              <option value="greek_general_ci" title="Greek, case-insensitive">greek_general_ci</option>
                                                          </optgroup>
                                                      <optgroup label="hebrew" title="ISO 8859-8 Hebrew">
                                                              <option value="hebrew_bin" title="Hebrew, binary">hebrew_bin</option>
                                                              <option value="hebrew_general_ci" title="Hebrew, case-insensitive">hebrew_general_ci</option>
                                                          </optgroup>
                                                      <optgroup label="hp8" title="HP West European">
                                                              <option value="hp8_bin" title="West European, binary">hp8_bin</option>
                                                              <option value="hp8_english_ci" title="English, case-insensitive">hp8_english_ci</option>
                                                          </optgroup>
                                                      <optgroup label="keybcs2" title="DOS Kamenicky Czech-Slovak">
                                                              <option value="keybcs2_bin" title="Czech-Slovak, binary">keybcs2_bin</option>
                                                              <option value="keybcs2_general_ci" title="Czech-Slovak, case-insensitive">keybcs2_general_ci</option>
                                                          </optgroup>
                                                      <optgroup label="koi8r" title="KOI8-R Relcom Russian">
                                                              <option value="koi8r_bin" title="Russian, binary">koi8r_bin</option>
                                                              <option value="koi8r_general_ci" title="Russian, case-insensitive">koi8r_general_ci</option>
                                                          </optgroup>
                                                      <optgroup label="koi8u" title="KOI8-U Ukrainian">
                                                              <option value="koi8u_bin" title="Ukrainian, binary">koi8u_bin</option>
                                                              <option value="koi8u_general_ci" title="Ukrainian, case-insensitive">koi8u_general_ci</option>
                                                          </optgroup>
                                                      <optgroup label="latin1" title="cp1252 West European">
                                                              <option value="latin1_bin" title="West European, binary">latin1_bin</option>
                                                              <option value="latin1_danish_ci" title="Danish, case-insensitive">latin1_danish_ci</option>
                                                              <option value="latin1_general_ci" title="West European, case-insensitive">latin1_general_ci</option>
                                                              <option value="latin1_general_cs" title="West European, case-sensitive">latin1_general_cs</option>
                                                              <option value="latin1_german1_ci" title="German (dictionary order), case-insensitive">latin1_german1_ci</option>
                                                              <option value="latin1_german2_ci" title="German (phone book order), case-insensitive">latin1_german2_ci</option>
                                                              <option value="latin1_spanish_ci" title="Spanish (modern), case-insensitive">latin1_spanish_ci</option>
                                                              <option value="latin1_swedish_ci" title="Swedish, case-insensitive">latin1_swedish_ci</option>
                                                          </optgroup>
                                                      <optgroup label="latin2" title="ISO 8859-2 Central European">
                                                              <option value="latin2_bin" title="Central European, binary">latin2_bin</option>
                                                              <option value="latin2_croatian_ci" title="Croatian, case-insensitive">latin2_croatian_ci</option>
                                                              <option value="latin2_czech_cs" title="Czech, case-sensitive">latin2_czech_cs</option>
                                                              <option value="latin2_general_ci" title="Central European, case-insensitive">latin2_general_ci</option>
                                                              <option value="latin2_hungarian_ci" title="Hungarian, case-insensitive">latin2_hungarian_ci</option>
                                                          </optgroup>
                                                      <optgroup label="latin5" title="ISO 8859-9 Turkish">
                                                              <option value="latin5_bin" title="Turkish, binary">latin5_bin</option>
                                                              <option value="latin5_turkish_ci" title="Turkish, case-insensitive">latin5_turkish_ci</option>
                                                          </optgroup>
                                                      <optgroup label="latin7" title="ISO 8859-13 Baltic">
                                                              <option value="latin7_bin" title="Baltic, binary">latin7_bin</option>
                                                              <option value="latin7_estonian_cs" title="Estonian, case-sensitive">latin7_estonian_cs</option>
                                                              <option value="latin7_general_ci" title="Baltic, case-insensitive">latin7_general_ci</option>
                                                              <option value="latin7_general_cs" title="Baltic, case-sensitive">latin7_general_cs</option>
                                                          </optgroup>
                                                      <optgroup label="macce" title="Mac Central European">
                                                              <option value="macce_bin" title="Central European, binary">macce_bin</option>
                                                              <option value="macce_general_ci" title="Central European, case-insensitive">macce_general_ci</option>
                                                          </optgroup>
                                                      <optgroup label="macroman" title="Mac West European">
                                                              <option value="macroman_bin" title="West European, binary">macroman_bin</option>
                                                              <option value="macroman_general_ci" title="West European, case-insensitive">macroman_general_ci</option>
                                                          </optgroup>
                                                      <optgroup label="sjis" title="Shift-JIS Japanese">
                                                              <option value="sjis_bin" title="Japanese, binary">sjis_bin</option>
                                                              <option value="sjis_japanese_ci" title="Japanese, case-insensitive">sjis_japanese_ci</option>
                                                          </optgroup>
                                                      <optgroup label="swe7" title="7bit Swedish">
                                                              <option value="swe7_bin" title="Swedish, binary">swe7_bin</option>
                                                              <option value="swe7_swedish_ci" title="Swedish, case-insensitive">swe7_swedish_ci</option>
                                                          </optgroup>
                                                      <optgroup label="tis620" title="TIS620 Thai">
                                                              <option value="tis620_bin" title="Thai, binary">tis620_bin</option>
                                                              <option value="tis620_thai_ci" title="Thai, case-insensitive">tis620_thai_ci</option>
                                                          </optgroup>
                                                      <optgroup label="ucs2" title="UCS-2 Unicode">
                                                              <option value="ucs2_bin" title="Unicode, binary">ucs2_bin</option>
                                                              <option value="ucs2_croatian_ci" title="Croatian, case-insensitive">ucs2_croatian_ci</option>
                                                              <option value="ucs2_czech_ci" title="Czech, case-insensitive">ucs2_czech_ci</option>
                                                              <option value="ucs2_danish_ci" title="Danish, case-insensitive">ucs2_danish_ci</option>
                                                              <option value="ucs2_esperanto_ci" title="Esperanto, case-insensitive">ucs2_esperanto_ci</option>
                                                              <option value="ucs2_estonian_ci" title="Estonian, case-insensitive">ucs2_estonian_ci</option>
                                                              <option value="ucs2_general_ci" title="Unicode, case-insensitive">ucs2_general_ci</option>
                                                              <option value="ucs2_general_mysql500_ci" title="Unicode (MySQL 5.0.0), case-insensitive">ucs2_general_mysql500_ci</option>
                                                              <option value="ucs2_german2_ci" title="German (phone book order), case-insensitive">ucs2_german2_ci</option>
                                                              <option value="ucs2_hungarian_ci" title="Hungarian, case-insensitive">ucs2_hungarian_ci</option>
                                                              <option value="ucs2_icelandic_ci" title="Icelandic, case-insensitive">ucs2_icelandic_ci</option>
                                                              <option value="ucs2_latvian_ci" title="Latvian, case-insensitive">ucs2_latvian_ci</option>
                                                              <option value="ucs2_lithuanian_ci" title="Lithuanian, case-insensitive">ucs2_lithuanian_ci</option>
                                                              <option value="ucs2_persian_ci" title="Persian, case-insensitive">ucs2_persian_ci</option>
                                                              <option value="ucs2_polish_ci" title="Polish, case-insensitive">ucs2_polish_ci</option>
                                                              <option value="ucs2_roman_ci" title="West European, case-insensitive">ucs2_roman_ci</option>
                                                              <option value="ucs2_romanian_ci" title="Romanian, case-insensitive">ucs2_romanian_ci</option>
                                                              <option value="ucs2_sinhala_ci" title="Sinhalese, case-insensitive">ucs2_sinhala_ci</option>
                                                              <option value="ucs2_slovak_ci" title="Slovak, case-insensitive">ucs2_slovak_ci</option>
                                                              <option value="ucs2_slovenian_ci" title="Slovenian, case-insensitive">ucs2_slovenian_ci</option>
                                                              <option value="ucs2_spanish2_ci" title="Spanish (traditional), case-insensitive">ucs2_spanish2_ci</option>
                                                              <option value="ucs2_spanish_ci" title="Spanish (modern), case-insensitive">ucs2_spanish_ci</option>
                                                              <option value="ucs2_swedish_ci" title="Swedish, case-insensitive">ucs2_swedish_ci</option>
                                                              <option value="ucs2_turkish_ci" title="Turkish, case-insensitive">ucs2_turkish_ci</option>
                                                              <option value="ucs2_unicode_520_ci" title="Unicode (UCA 5.2.0), case-insensitive">ucs2_unicode_520_ci</option>
                                                              <option value="ucs2_unicode_ci" title="Unicode, case-insensitive">ucs2_unicode_ci</option>
                                                              <option value="ucs2_vietnamese_ci" title="Vietnamese, case-insensitive">ucs2_vietnamese_ci</option>
                                                          </optgroup>
                                                      <optgroup label="ujis" title="EUC-JP Japanese">
                                                              <option value="ujis_bin" title="Japanese, binary">ujis_bin</option>
                                                              <option value="ujis_japanese_ci" title="Japanese, case-insensitive">ujis_japanese_ci</option>
                                                          </optgroup>
                                                      <optgroup label="utf16" title="UTF-16 Unicode">
                                                              <option value="utf16_bin" title="Unicode, binary">utf16_bin</option>
                                                              <option value="utf16_croatian_ci" title="Croatian, case-insensitive">utf16_croatian_ci</option>
                                                              <option value="utf16_czech_ci" title="Czech, case-insensitive">utf16_czech_ci</option>
                                                              <option value="utf16_danish_ci" title="Danish, case-insensitive">utf16_danish_ci</option>
                                                              <option value="utf16_esperanto_ci" title="Esperanto, case-insensitive">utf16_esperanto_ci</option>
                                                              <option value="utf16_estonian_ci" title="Estonian, case-insensitive">utf16_estonian_ci</option>
                                                              <option value="utf16_general_ci" title="Unicode, case-insensitive">utf16_general_ci</option>
                                                              <option value="utf16_german2_ci" title="German (phone book order), case-insensitive">utf16_german2_ci</option>
                                                              <option value="utf16_hungarian_ci" title="Hungarian, case-insensitive">utf16_hungarian_ci</option>
                                                              <option value="utf16_icelandic_ci" title="Icelandic, case-insensitive">utf16_icelandic_ci</option>
                                                              <option value="utf16_latvian_ci" title="Latvian, case-insensitive">utf16_latvian_ci</option>
                                                              <option value="utf16_lithuanian_ci" title="Lithuanian, case-insensitive">utf16_lithuanian_ci</option>
                                                              <option value="utf16_persian_ci" title="Persian, case-insensitive">utf16_persian_ci</option>
                                                              <option value="utf16_polish_ci" title="Polish, case-insensitive">utf16_polish_ci</option>
                                                              <option value="utf16_roman_ci" title="West European, case-insensitive">utf16_roman_ci</option>
                                                              <option value="utf16_romanian_ci" title="Romanian, case-insensitive">utf16_romanian_ci</option>
                                                              <option value="utf16_sinhala_ci" title="Sinhalese, case-insensitive">utf16_sinhala_ci</option>
                                                              <option value="utf16_slovak_ci" title="Slovak, case-insensitive">utf16_slovak_ci</option>
                                                              <option value="utf16_slovenian_ci" title="Slovenian, case-insensitive">utf16_slovenian_ci</option>
                                                              <option value="utf16_spanish2_ci" title="Spanish (traditional), case-insensitive">utf16_spanish2_ci</option>
                                                              <option value="utf16_spanish_ci" title="Spanish (modern), case-insensitive">utf16_spanish_ci</option>
                                                              <option value="utf16_swedish_ci" title="Swedish, case-insensitive">utf16_swedish_ci</option>
                                                              <option value="utf16_turkish_ci" title="Turkish, case-insensitive">utf16_turkish_ci</option>
                                                              <option value="utf16_unicode_520_ci" title="Unicode (UCA 5.2.0), case-insensitive">utf16_unicode_520_ci</option>
                                                              <option value="utf16_unicode_ci" title="Unicode, case-insensitive">utf16_unicode_ci</option>
                                                              <option value="utf16_vietnamese_ci" title="Vietnamese, case-insensitive">utf16_vietnamese_ci</option>
                                                          </optgroup>
                                                      <optgroup label="utf16le" title="UTF-16LE Unicode">
                                                              <option value="utf16le_bin" title="Unicode, binary">utf16le_bin</option>
                                                              <option value="utf16le_general_ci" title="Unicode, case-insensitive">utf16le_general_ci</option>
                                                          </optgroup>
                                                      <optgroup label="utf32" title="UTF-32 Unicode">
                                                              <option value="utf32_bin" title="Unicode, binary">utf32_bin</option>
                                                              <option value="utf32_croatian_ci" title="Croatian, case-insensitive">utf32_croatian_ci</option>
                                                              <option value="utf32_czech_ci" title="Czech, case-insensitive">utf32_czech_ci</option>
                                                              <option value="utf32_danish_ci" title="Danish, case-insensitive">utf32_danish_ci</option>
                                                              <option value="utf32_esperanto_ci" title="Esperanto, case-insensitive">utf32_esperanto_ci</option>
                                                              <option value="utf32_estonian_ci" title="Estonian, case-insensitive">utf32_estonian_ci</option>
                                                              <option value="utf32_general_ci" title="Unicode, case-insensitive">utf32_general_ci</option>
                                                              <option value="utf32_german2_ci" title="German (phone book order), case-insensitive">utf32_german2_ci</option>
                                                              <option value="utf32_hungarian_ci" title="Hungarian, case-insensitive">utf32_hungarian_ci</option>
                                                              <option value="utf32_icelandic_ci" title="Icelandic, case-insensitive">utf32_icelandic_ci</option>
                                                              <option value="utf32_latvian_ci" title="Latvian, case-insensitive">utf32_latvian_ci</option>
                                                              <option value="utf32_lithuanian_ci" title="Lithuanian, case-insensitive">utf32_lithuanian_ci</option>
                                                              <option value="utf32_persian_ci" title="Persian, case-insensitive">utf32_persian_ci</option>
                                                              <option value="utf32_polish_ci" title="Polish, case-insensitive">utf32_polish_ci</option>
                                                              <option value="utf32_roman_ci" title="West European, case-insensitive">utf32_roman_ci</option>
                                                              <option value="utf32_romanian_ci" title="Romanian, case-insensitive">utf32_romanian_ci</option>
                                                              <option value="utf32_sinhala_ci" title="Sinhalese, case-insensitive">utf32_sinhala_ci</option>
                                                              <option value="utf32_slovak_ci" title="Slovak, case-insensitive">utf32_slovak_ci</option>
                                                              <option value="utf32_slovenian_ci" title="Slovenian, case-insensitive">utf32_slovenian_ci</option>
                                                              <option value="utf32_spanish2_ci" title="Spanish (traditional), case-insensitive">utf32_spanish2_ci</option>
                                                              <option value="utf32_spanish_ci" title="Spanish (modern), case-insensitive">utf32_spanish_ci</option>
                                                              <option value="utf32_swedish_ci" title="Swedish, case-insensitive">utf32_swedish_ci</option>
                                                              <option value="utf32_turkish_ci" title="Turkish, case-insensitive">utf32_turkish_ci</option>
                                                              <option value="utf32_unicode_520_ci" title="Unicode (UCA 5.2.0), case-insensitive">utf32_unicode_520_ci</option>
                                                              <option value="utf32_unicode_ci" title="Unicode, case-insensitive">utf32_unicode_ci</option>
                                                              <option value="utf32_vietnamese_ci" title="Vietnamese, case-insensitive">utf32_vietnamese_ci</option>
                                                          </optgroup>
                                                      <optgroup label="utf8mb3" title="UTF-8 Unicode">
                                                              <option value="utf8mb3_bin" title="Unicode, binary">utf8mb3_bin</option>
                                                              <option value="utf8mb3_croatian_ci" title="Croatian, case-insensitive">utf8mb3_croatian_ci</option>
                                                              <option value="utf8mb3_czech_ci" title="Czech, case-insensitive">utf8mb3_czech_ci</option>
                                                              <option value="utf8mb3_danish_ci" title="Danish, case-insensitive">utf8mb3_danish_ci</option>
                                                              <option value="utf8mb3_esperanto_ci" title="Esperanto, case-insensitive">utf8mb3_esperanto_ci</option>
                                                              <option value="utf8mb3_estonian_ci" title="Estonian, case-insensitive">utf8mb3_estonian_ci</option>
                                                              <option value="utf8mb3_general_ci" title="Unicode, case-insensitive">utf8mb3_general_ci</option>
                                                              <option value="utf8mb3_general_mysql500_ci" title="Unicode (MySQL 5.0.0), case-insensitive">utf8mb3_general_mysql500_ci</option>
                                                              <option value="utf8mb3_german2_ci" title="German (phone book order), case-insensitive">utf8mb3_german2_ci</option>
                                                              <option value="utf8mb3_hungarian_ci" title="Hungarian, case-insensitive">utf8mb3_hungarian_ci</option>
                                                              <option value="utf8mb3_icelandic_ci" title="Icelandic, case-insensitive">utf8mb3_icelandic_ci</option>
                                                              <option value="utf8mb3_latvian_ci" title="Latvian, case-insensitive">utf8mb3_latvian_ci</option>
                                                              <option value="utf8mb3_lithuanian_ci" title="Lithuanian, case-insensitive">utf8mb3_lithuanian_ci</option>
                                                              <option value="utf8mb3_persian_ci" title="Persian, case-insensitive">utf8mb3_persian_ci</option>
                                                              <option value="utf8mb3_polish_ci" title="Polish, case-insensitive">utf8mb3_polish_ci</option>
                                                              <option value="utf8mb3_roman_ci" title="West European, case-insensitive">utf8mb3_roman_ci</option>
                                                              <option value="utf8mb3_romanian_ci" title="Romanian, case-insensitive">utf8mb3_romanian_ci</option>
                                                              <option value="utf8mb3_sinhala_ci" title="Sinhalese, case-insensitive">utf8mb3_sinhala_ci</option>
                                                              <option value="utf8mb3_slovak_ci" title="Slovak, case-insensitive">utf8mb3_slovak_ci</option>
                                                              <option value="utf8mb3_slovenian_ci" title="Slovenian, case-insensitive">utf8mb3_slovenian_ci</option>
                                                              <option value="utf8mb3_spanish2_ci" title="Spanish (traditional), case-insensitive">utf8mb3_spanish2_ci</option>
                                                              <option value="utf8mb3_spanish_ci" title="Spanish (modern), case-insensitive">utf8mb3_spanish_ci</option>
                                                              <option value="utf8mb3_swedish_ci" title="Swedish, case-insensitive">utf8mb3_swedish_ci</option>
                                                              <option value="utf8mb3_tolower_ci" title="Unicode, case-insensitive">utf8mb3_tolower_ci</option>
                                                              <option value="utf8mb3_turkish_ci" title="Turkish, case-insensitive">utf8mb3_turkish_ci</option>
                                                              <option value="utf8mb3_unicode_520_ci" title="Unicode (UCA 5.2.0), case-insensitive">utf8mb3_unicode_520_ci</option>
                                                              <option value="utf8mb3_unicode_ci" title="Unicode, case-insensitive">utf8mb3_unicode_ci</option>
                                                              <option value="utf8mb3_vietnamese_ci" title="Vietnamese, case-insensitive">utf8mb3_vietnamese_ci</option>
                                                          </optgroup>
                                                      <optgroup label="utf8mb4" title="UTF-8 Unicode">
                                                              <option value="utf8mb4_0900_ai_ci" title="Unicode (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_0900_ai_ci</option>
                                                              <option value="utf8mb4_0900_as_ci" title="Unicode (UCA 9.0.0), accent-sensitive, case-insensitive">utf8mb4_0900_as_ci</option>
                                                              <option value="utf8mb4_0900_as_cs" title="Unicode (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_0900_as_cs</option>
                                                              <option value="utf8mb4_0900_bin" title="Unicode (UCA 9.0.0), binary">utf8mb4_0900_bin</option>
                                                              <option value="utf8mb4_bg_0900_ai_ci" title="Bulgarian (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_bg_0900_ai_ci</option>
                                                              <option value="utf8mb4_bg_0900_as_cs" title="Bulgarian (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_bg_0900_as_cs</option>
                                                              <option value="utf8mb4_bin" title="Unicode (UCA 4.0.0), binary">utf8mb4_bin</option>
                                                              <option value="utf8mb4_bs_0900_ai_ci" title="Unicode (UCA 4.0.0), accent-insensitive, case-insensitive">utf8mb4_bs_0900_ai_ci</option>
                                                              <option value="utf8mb4_bs_0900_as_cs" title="Unicode (UCA 4.0.0), accent-sensitive, case-sensitive">utf8mb4_bs_0900_as_cs</option>
                                                              <option value="utf8mb4_croatian_ci" title="Croatian (UCA 4.0.0), case-insensitive">utf8mb4_croatian_ci</option>
                                                              <option value="utf8mb4_cs_0900_ai_ci" title="Czech (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_cs_0900_ai_ci</option>
                                                              <option value="utf8mb4_cs_0900_as_cs" title="Czech (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_cs_0900_as_cs</option>
                                                              <option value="utf8mb4_czech_ci" title="Czech (UCA 4.0.0), case-insensitive">utf8mb4_czech_ci</option>
                                                              <option value="utf8mb4_da_0900_ai_ci" title="Danish (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_da_0900_ai_ci</option>
                                                              <option value="utf8mb4_da_0900_as_cs" title="Danish (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_da_0900_as_cs</option>
                                                              <option value="utf8mb4_danish_ci" title="Danish (UCA 4.0.0), case-insensitive">utf8mb4_danish_ci</option>
                                                              <option value="utf8mb4_de_pb_0900_ai_ci" title="German (phone book order) (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_de_pb_0900_ai_ci</option>
                                                              <option value="utf8mb4_de_pb_0900_as_cs" title="German (phone book order) (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_de_pb_0900_as_cs</option>
                                                              <option value="utf8mb4_eo_0900_ai_ci" title="Esperanto (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_eo_0900_ai_ci</option>
                                                              <option value="utf8mb4_eo_0900_as_cs" title="Esperanto (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_eo_0900_as_cs</option>
                                                              <option value="utf8mb4_es_0900_ai_ci" title="Spanish (modern) (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_es_0900_ai_ci</option>
                                                              <option value="utf8mb4_es_0900_as_cs" title="Spanish (modern) (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_es_0900_as_cs</option>
                                                              <option value="utf8mb4_es_trad_0900_ai_ci" title="Spanish (traditional) (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_es_trad_0900_ai_ci</option>
                                                              <option value="utf8mb4_es_trad_0900_as_cs" title="Spanish (traditional) (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_es_trad_0900_as_cs</option>
                                                              <option value="utf8mb4_esperanto_ci" title="Esperanto (UCA 4.0.0), case-insensitive">utf8mb4_esperanto_ci</option>
                                                              <option value="utf8mb4_estonian_ci" title="Estonian (UCA 4.0.0), case-insensitive">utf8mb4_estonian_ci</option>
                                                              <option value="utf8mb4_et_0900_ai_ci" title="Estonian (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_et_0900_ai_ci</option>
                                                              <option value="utf8mb4_et_0900_as_cs" title="Estonian (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_et_0900_as_cs</option>
                                                              <option value="utf8mb4_general_ci" title="Unicode (UCA 4.0.0), case-insensitive">utf8mb4_general_ci</option>
                                                              <option value="utf8mb4_german2_ci" title="German (phone book order) (UCA 4.0.0), case-insensitive">utf8mb4_german2_ci</option>
                                                              <option value="utf8mb4_gl_0900_ai_ci" title="Unicode (UCA 4.0.0), accent-insensitive, case-insensitive">utf8mb4_gl_0900_ai_ci</option>
                                                              <option value="utf8mb4_gl_0900_as_cs" title="Unicode (UCA 4.0.0), accent-sensitive, case-sensitive">utf8mb4_gl_0900_as_cs</option>
                                                              <option value="utf8mb4_hr_0900_ai_ci" title="Croatian (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_hr_0900_ai_ci</option>
                                                              <option value="utf8mb4_hr_0900_as_cs" title="Croatian (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_hr_0900_as_cs</option>
                                                              <option value="utf8mb4_hu_0900_ai_ci" title="Hungarian (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_hu_0900_ai_ci</option>
                                                              <option value="utf8mb4_hu_0900_as_cs" title="Hungarian (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_hu_0900_as_cs</option>
                                                              <option value="utf8mb4_hungarian_ci" title="Hungarian (UCA 4.0.0), case-insensitive">utf8mb4_hungarian_ci</option>
                                                              <option value="utf8mb4_icelandic_ci" title="Icelandic (UCA 4.0.0), case-insensitive">utf8mb4_icelandic_ci</option>
                                                              <option value="utf8mb4_is_0900_ai_ci" title="Icelandic (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_is_0900_ai_ci</option>
                                                              <option value="utf8mb4_is_0900_as_cs" title="Icelandic (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_is_0900_as_cs</option>
                                                              <option value="utf8mb4_ja_0900_as_cs" title="Japanese (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_ja_0900_as_cs</option>
                                                              <option value="utf8mb4_ja_0900_as_cs_ks" title="Japanese (UCA 9.0.0), accent-sensitive, case-sensitive, kana-sensitive">utf8mb4_ja_0900_as_cs_ks</option>
                                                              <option value="utf8mb4_la_0900_ai_ci" title="Classical Latin (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_la_0900_ai_ci</option>
                                                              <option value="utf8mb4_la_0900_as_cs" title="Classical Latin (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_la_0900_as_cs</option>
                                                              <option value="utf8mb4_latvian_ci" title="Latvian (UCA 4.0.0), case-insensitive">utf8mb4_latvian_ci</option>
                                                              <option value="utf8mb4_lithuanian_ci" title="Lithuanian (UCA 4.0.0), case-insensitive">utf8mb4_lithuanian_ci</option>
                                                              <option value="utf8mb4_lt_0900_ai_ci" title="Lithuanian (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_lt_0900_ai_ci</option>
                                                              <option value="utf8mb4_lt_0900_as_cs" title="Lithuanian (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_lt_0900_as_cs</option>
                                                              <option value="utf8mb4_lv_0900_ai_ci" title="Latvian (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_lv_0900_ai_ci</option>
                                                              <option value="utf8mb4_lv_0900_as_cs" title="Latvian (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_lv_0900_as_cs</option>
                                                              <option value="utf8mb4_mn_cyrl_0900_ai_ci" title="Unicode (UCA 4.0.0), accent-insensitive, case-insensitive">utf8mb4_mn_cyrl_0900_ai_ci</option>
                                                              <option value="utf8mb4_mn_cyrl_0900_as_cs" title="Unicode (UCA 4.0.0), accent-sensitive, case-sensitive">utf8mb4_mn_cyrl_0900_as_cs</option>
                                                              <option value="utf8mb4_nb_0900_ai_ci" title="Unicode (UCA 4.0.0), accent-insensitive, case-insensitive">utf8mb4_nb_0900_ai_ci</option>
                                                              <option value="utf8mb4_nb_0900_as_cs" title="Unicode (UCA 4.0.0), accent-sensitive, case-sensitive">utf8mb4_nb_0900_as_cs</option>
                                                              <option value="utf8mb4_nn_0900_ai_ci" title="Unicode (UCA 4.0.0), accent-insensitive, case-insensitive">utf8mb4_nn_0900_ai_ci</option>
                                                              <option value="utf8mb4_nn_0900_as_cs" title="Unicode (UCA 4.0.0), accent-sensitive, case-sensitive">utf8mb4_nn_0900_as_cs</option>
                                                              <option value="utf8mb4_persian_ci" title="Persian (UCA 4.0.0), case-insensitive">utf8mb4_persian_ci</option>
                                                              <option value="utf8mb4_pl_0900_ai_ci" title="Polish (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_pl_0900_ai_ci</option>
                                                              <option value="utf8mb4_pl_0900_as_cs" title="Polish (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_pl_0900_as_cs</option>
                                                              <option value="utf8mb4_polish_ci" title="Polish (UCA 4.0.0), case-insensitive">utf8mb4_polish_ci</option>
                                                              <option value="utf8mb4_ro_0900_ai_ci" title="Romanian (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_ro_0900_ai_ci</option>
                                                              <option value="utf8mb4_ro_0900_as_cs" title="Romanian (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_ro_0900_as_cs</option>
                                                              <option value="utf8mb4_roman_ci" title="West European (UCA 4.0.0), case-insensitive">utf8mb4_roman_ci</option>
                                                              <option value="utf8mb4_romanian_ci" title="Romanian (UCA 4.0.0), case-insensitive">utf8mb4_romanian_ci</option>
                                                              <option value="utf8mb4_ru_0900_ai_ci" title="Russian (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_ru_0900_ai_ci</option>
                                                              <option value="utf8mb4_ru_0900_as_cs" title="Russian (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_ru_0900_as_cs</option>
                                                              <option value="utf8mb4_sinhala_ci" title="Sinhalese (UCA 4.0.0), case-insensitive">utf8mb4_sinhala_ci</option>
                                                              <option value="utf8mb4_sk_0900_ai_ci" title="Slovak (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_sk_0900_ai_ci</option>
                                                              <option value="utf8mb4_sk_0900_as_cs" title="Slovak (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_sk_0900_as_cs</option>
                                                              <option value="utf8mb4_sl_0900_ai_ci" title="Slovenian (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_sl_0900_ai_ci</option>
                                                              <option value="utf8mb4_sl_0900_as_cs" title="Slovenian (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_sl_0900_as_cs</option>
                                                              <option value="utf8mb4_slovak_ci" title="Slovak (UCA 4.0.0), case-insensitive">utf8mb4_slovak_ci</option>
                                                              <option value="utf8mb4_slovenian_ci" title="Slovenian (UCA 4.0.0), case-insensitive">utf8mb4_slovenian_ci</option>
                                                              <option value="utf8mb4_spanish2_ci" title="Spanish (traditional) (UCA 4.0.0), case-insensitive">utf8mb4_spanish2_ci</option>
                                                              <option value="utf8mb4_spanish_ci" title="Spanish (modern) (UCA 4.0.0), case-insensitive">utf8mb4_spanish_ci</option>
                                                              <option value="utf8mb4_sr_latn_0900_ai_ci" title="Unicode (UCA 4.0.0), accent-insensitive, case-insensitive">utf8mb4_sr_latn_0900_ai_ci</option>
                                                              <option value="utf8mb4_sr_latn_0900_as_cs" title="Unicode (UCA 4.0.0), accent-sensitive, case-sensitive">utf8mb4_sr_latn_0900_as_cs</option>
                                                              <option value="utf8mb4_sv_0900_ai_ci" title="Swedish (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_sv_0900_ai_ci</option>
                                                              <option value="utf8mb4_sv_0900_as_cs" title="Swedish (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_sv_0900_as_cs</option>
                                                              <option value="utf8mb4_swedish_ci" title="Swedish (UCA 4.0.0), case-insensitive">utf8mb4_swedish_ci</option>
                                                              <option value="utf8mb4_tr_0900_ai_ci" title="Turkish (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_tr_0900_ai_ci</option>
                                                              <option value="utf8mb4_tr_0900_as_cs" title="Turkish (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_tr_0900_as_cs</option>
                                                              <option value="utf8mb4_turkish_ci" title="Turkish (UCA 4.0.0), case-insensitive">utf8mb4_turkish_ci</option>
                                                              <option value="utf8mb4_unicode_520_ci" title="Unicode (UCA 5.2.0), case-insensitive">utf8mb4_unicode_520_ci</option>
                                                              <option value="utf8mb4_unicode_ci" title="Unicode (UCA 4.0.0), case-insensitive" selected>utf8mb4_unicode_ci</option>
                                                              <option value="utf8mb4_vi_0900_ai_ci" title="Vietnamese (UCA 9.0.0), accent-insensitive, case-insensitive">utf8mb4_vi_0900_ai_ci</option>
                                                              <option value="utf8mb4_vi_0900_as_cs" title="Vietnamese (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_vi_0900_as_cs</option>
                                                              <option value="utf8mb4_vietnamese_ci" title="Vietnamese (UCA 4.0.0), case-insensitive">utf8mb4_vietnamese_ci</option>
                                                              <option value="utf8mb4_zh_0900_as_cs" title="Chinese (UCA 9.0.0), accent-sensitive, case-sensitive">utf8mb4_zh_0900_as_cs</option>
                                                          </optgroup>
                                                  </select>
                      </div>
                                          </form>
                  </li>

                  <li id="li_user_preferences" class="list-group-item">
                    <a href="index.php?route=/preferences/manage&lang=en">
                      <span class="text-nowrap"><img src="themes/dot.gif" title="More settings" alt="More settings" class="icon ic_b_tblops">&nbsp;More settings</span>
                    </a>
                  </li>
                              </ul>
            </div>
          
                        <div class="card mt-4">
              <div class="card-header">
                Appearance settings              </div>
              <ul class="list-group list-group-flush">
                                  <li id="li_select_lang" class="list-group-item">
                    <form method="get" action="index.php?route=/&lang=en" class="row row-cols-lg-auto align-items-center disableAjax">
                      <input type="hidden" name="db" value=""><input type="hidden" name="table" value=""><input type="hidden" name="lang" value="en"><input type="hidden" name="token" value="562c222656407b5f2b4d7e3264497221">
                      <div class="col-12">
                        <label for="languageSelect" class="col-form-label text-nowrap">
                          <img src="themes/dot.gif" title="" alt="" class="icon ic_s_lang">
                          Language                                                    <a href="./doc/html/faq.html#faq7-2" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
                        </label>
                      </div>
                      <div class="col-12">
                        <select name="lang" class="form-select autosubmit w-auto" lang="en" dir="ltr" id="languageSelect">
                                                      <option value="sq">Shqip - Albanian</option>
                                                      <option value="ar">&#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577; - Arabic</option>
                                                      <option value="hy">Հայերէն - Armenian</option>
                                                      <option value="az">Az&#601;rbaycanca - Azerbaijani</option>
                                                      <option value="bn">বাংলা - Bangla</option>
                                                      <option value="be">&#1041;&#1077;&#1083;&#1072;&#1088;&#1091;&#1089;&#1082;&#1072;&#1103; - Belarusian</option>
                                                      <option value="bg">&#1041;&#1098;&#1083;&#1075;&#1072;&#1088;&#1089;&#1082;&#1080; - Bulgarian</option>
                                                      <option value="ca">Catal&agrave; - Catalan</option>
                                                      <option value="zh_cn">&#20013;&#25991; - Chinese simplified</option>
                                                      <option value="zh_tw">&#20013;&#25991; - Chinese traditional</option>
                                                      <option value="cs">Čeština - Czech</option>
                                                      <option value="da">Dansk - Danish</option>
                                                      <option value="nl">Nederlands - Dutch</option>
                                                      <option value="en" selected>English</option>
                                                      <option value="en_gb">English (United Kingdom)</option>
                                                      <option value="et">Eesti - Estonian</option>
                                                      <option value="fi">Suomi - Finnish</option>
                                                      <option value="fr">Fran&ccedil;ais - French</option>
                                                      <option value="gl">Galego - Galician</option>
                                                      <option value="ka">&#4325;&#4304;&#4320;&#4311;&#4323;&#4314;&#4312; - Georgian</option>
                                                      <option value="de">Deutsch - German</option>
                                                      <option value="el">&Epsilon;&lambda;&lambda;&eta;&nu;&iota;&kappa;&#940; - Greek</option>
                                                      <option value="he">&#1506;&#1489;&#1512;&#1497;&#1514; - Hebrew</option>
                                                      <option value="hu">Magyar - Hungarian</option>
                                                      <option value="id">Bahasa Indonesia - Indonesian</option>
                                                      <option value="ia">Interlingua</option>
                                                      <option value="it">Italiano - Italian</option>
                                                      <option value="ja">&#26085;&#26412;&#35486; - Japanese</option>
                                                      <option value="kk">Қазақ - Kazakh</option>
                                                      <option value="ko">&#54620;&#44397;&#50612; - Korean</option>
                                                      <option value="nb">Norsk - Norwegian</option>
                                                      <option value="pl">Polski - Polish</option>
                                                      <option value="pt">Portugu&ecirc;s - Portuguese</option>
                                                      <option value="pt_br">Portugu&ecirc;s (Brasil) - Portuguese (Brazil)</option>
                                                      <option value="ro">Rom&acirc;n&#259; - Romanian</option>
                                                      <option value="ru">&#1056;&#1091;&#1089;&#1089;&#1082;&#1080;&#1081; - Russian</option>
                                                      <option value="si">&#3523;&#3538;&#3458;&#3524;&#3517; - Sinhala</option>
                                                      <option value="sk">Sloven&#269;ina - Slovak</option>
                                                      <option value="sl">Sloven&scaron;&#269;ina - Slovenian</option>
                                                      <option value="es">Espa&ntilde;ol - Spanish</option>
                                                      <option value="sv">Svenska - Swedish</option>
                                                      <option value="tr">T&uuml;rk&ccedil;e - Turkish</option>
                                                      <option value="uk">&#1059;&#1082;&#1088;&#1072;&#1111;&#1085;&#1089;&#1100;&#1082;&#1072; - Ukrainian</option>
                                                      <option value="ug">ئۇيغۇرچە - Uyghur</option>
                                                      <option value="vi">Tiếng Việt - Vietnamese</option>
                                                  </select>
                      </div>
                    </form>
                  </li>
                
                                  <li id="li_select_theme" class="list-group-item">
                    <form method="post" action="index.php?route=/themes/set&lang=en" class="row row-cols-lg-auto align-items-center disableAjax">
                      <input type="hidden" name="lang" value="en"><input type="hidden" name="token" value="562c222656407b5f2b4d7e3264497221">
                      <div class="col-12">
                        <label for="themeSelect" class="col-form-label">
                          <span class="text-nowrap"><img src="themes/dot.gif" title="Theme" alt="Theme" class="icon ic_s_theme">&nbsp;Theme</span>
                        </label>
                      </div>
                      <div class="col-12">
                        <div class="input-group">
                          <select name="set_theme" class="form-select autosubmit" lang="en" dir="ltr" id="themeSelect">
                                                          <option value="bootstrap">Bootstrap</option>
                                                          <option value="metro">Metro</option>
                                                          <option value="original">Original</option>
                                                          <option value="pmahomme" selected>pmahomme</option>
                                                      </select>
                          <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#themesModal">
                            View all                          </button>
                        </div>
                      </div>
                    </form>
                  </li>
                              </ul>
            </div>
                      </div>

      <div class="col-lg-5 col-12">
                  <div class="card mt-4">
            <div class="card-header">
              Database server            </div>
            <ul class="list-group list-group-flush">
              <li class="list-group-item">
                Server:                mysql via TCP/IP
              </li>
              <li class="list-group-item">
                Server type:                MySQL
              </li>
              <li class="list-group-item">
                Server connection:                <span class="text-danger">SSL is not being used</span> <a href="./doc/html/setup.html#ssl" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
              </li>
              <li class="list-group-item">
                Server version:                8.0.42 - MySQL Community Server - GPL
              </li>
              <li class="list-group-item">
                Protocol version:                10
              </li>
              <li class="list-group-item">
                User:                root@**********
              </li>
              <li class="list-group-item">
                Server charset:                <span lang="en" dir="ltr">
                  UTF-8 Unicode (utf8mb4)
                </span>
              </li>
            </ul>
          </div>
        
                  <div class="card mt-4">
            <div class="card-header">
              Web server            </div>
            <ul class="list-group list-group-flush">
                                              <li class="list-group-item">
                  Apache/2.4.62 (Debian)
                </li>
                                <li class="list-group-item" id="li_mysql_client_version">
                  Database client version:                  libmysql - mysqlnd 8.2.27
                </li>
                <li class="list-group-item">
                  PHP extension:                                      mysqli
                    <a href="./url.php?url=https%3A%2F%2Fwww.php.net%2Fmanual%2Fen%2Fbook.mysqli.php" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
                                      curl
                    <a href="./url.php?url=https%3A%2F%2Fwww.php.net%2Fmanual%2Fen%2Fbook.curl.php" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
                                      mbstring
                    <a href="./url.php?url=https%3A%2F%2Fwww.php.net%2Fmanual%2Fen%2Fbook.mbstring.php" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
                                      sodium
                    <a href="./url.php?url=https%3A%2F%2Fwww.php.net%2Fmanual%2Fen%2Fbook.sodium.php" target="documentation"><img src="themes/dot.gif" title="Documentation" alt="Documentation" class="icon ic_b_help"></a>
                                  </li>
                <li class="list-group-item">
                  PHP version:                  8.2.27
                </li>
                                        </ul>
          </div>
        
          <div class="card mt-4">
            <div class="card-header">
              phpMyAdmin
            </div>
            <ul class="list-group list-group-flush">
              <li id="li_pma_version" class="list-group-item jsversioncheck">
                Version information:                <span class="version">5.2.2</span>
              </li>
              <li class="list-group-item">
                <a href="./doc/html/index.html" target="_blank" rel="noopener noreferrer">
                  Documentation                </a>
              </li>
              <li class="list-group-item">
                <a href="./url.php?url=https%3A%2F%2Fwww.phpmyadmin.net%2F" target="_blank" rel="noopener noreferrer">
                  Official Homepage                </a>
              </li>
              <li class="list-group-item">
                <a href="./url.php?url=https%3A%2F%2Fwww.phpmyadmin.net%2Fcontribute%2F" target="_blank" rel="noopener noreferrer">
                  Contribute                </a>
              </li>
              <li class="list-group-item">
                <a href="./url.php?url=https%3A%2F%2Fwww.phpmyadmin.net%2Fsupport%2F" target="_blank" rel="noopener noreferrer">
                  Get support                </a>
              </li>
              <li class="list-group-item">
                <a href="index.php?route=/changelog&lang=en" target="_blank">
                  List of changes                </a>
              </li>
              <li class="list-group-item">
                <a href="index.php?route=/license&lang=en" target="_blank">
                  License                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>

          </div>
  </div>

  <div class="modal fade" id="themesModal" tabindex="-1" aria-labelledby="themesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="themesModalLabel">phpMyAdmin Themes</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading…</span>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <a href="./url.php?url=https%3A%2F%2Fwww.phpmyadmin.net%2Fthemes%2F#pma_5_2" class="btn btn-primary" rel="noopener noreferrer" target="_blank">
            Get more themes!          </a>
        </div>
      </div>
    </div>
  </div>

<div class="alert alert-primary" role="alert">
  <img src="themes/dot.gif" title="" alt="" class="icon ic_s_notice"> The phpMyAdmin configuration storage is not completely configured, some extended features have been deactivated. <a href="index.php?route=/check-relations&lang=en" data-post="?lang=en">Find out why</a>. <br>Or alternately go to 'Operations' tab of any database to set it up there.
</div>

  </div>
      <div id="selflink" class="d-print-none">
      <a href="index.php?route=%2F&amp;server=1&amp;lang=en" title="Open new phpMyAdmin window" target="_blank" rel="noopener noreferrer">
                  <img src="themes/dot.gif" title="Open new phpMyAdmin window" alt="Open new phpMyAdmin window" class="icon ic_window-new">
              </a>
    </div>
  
  <div class="clearfloat d-print-none" id="pma_errors">
    
  </div>

  
<script data-cfasync="false" type="text/javascript">
// <![CDATA[
var debugSQLInfo = 'null';

// ]]>
</script>


  
  
  </body>
</html>
