Write-Host "🔧 Fixing SSL certificates for 51Club Casino..." -ForegroundColor Green

# Wait for nginx container to be ready
Write-Host "Waiting for nginx container to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Generate SSL certificates for all domains
$domains = @("51club.local", "api.51club.local", "games.51club.local", "admin.51club.local", "db.51club.local")

foreach ($domain in $domains) {
    Write-Host "Generating SSL certificate for $domain..." -ForegroundColor Cyan
    
    $cmd = "docker exec 51club_nginx openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout `"/etc/nginx/ssl/$domain.key`" -out `"/etc/nginx/ssl/$domain.crt`" -subj `"/C=US/ST=State/L=City/O=51Club/CN=$domain`""
    
    try {
        Invoke-Expression $cmd 2>$null
        Write-Host "✅ Certificate generated for $domain" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️ Failed to generate certificate for $domain" -ForegroundColor Red
    }
}

Write-Host "Restarting nginx..." -ForegroundColor Yellow
docker-compose restart nginx

Write-Host "✅ SSL certificates generated and nginx restarted!" -ForegroundColor Green
Write-Host "🌐 Services should now be accessible at:" -ForegroundColor Cyan
Write-Host "   - Main Site: https://localhost:8445 (or http://localhost:8085)" -ForegroundColor White
Write-Host "   - Database: MySQL on localhost:3308" -ForegroundColor White
Write-Host "   - Games API: Available via container networking" -ForegroundColor White
