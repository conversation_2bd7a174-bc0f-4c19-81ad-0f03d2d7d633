@echo off
title 51Club Casino - Clean URL Setup

echo.
echo ========================================
echo   51Club Casino - Clean URL Setup
echo ========================================
echo.
echo This will add domain entries to your hosts file to enable clean URLs:
echo   - http://51club.local:8085
echo   - http://api.51club.local:8085
echo   - http://games.51club.local:8085
echo   - http://admin.51club.local:8085
echo   - http://db.51club.local:8085
echo.

REM Check for admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Administrator privileges confirmed.
    echo.
) else (
    echo [ERROR] This script requires Administrator privileges!
    echo.
    echo Please:
    echo 1. Right-click on this file
    echo 2. Select "Run as administrator"
    echo 3. Click "Yes" when prompted
    echo.
    pause
    exit /b 1
)

echo Adding entries to hosts file...
echo.

REM Backup hosts file
copy "%SystemRoot%\System32\drivers\etc\hosts" "%SystemRoot%\System32\drivers\etc\hosts.backup.%date:~-4,4%%date:~-10,2%%date:~-7,2%" >nul 2>&1
echo [OK] Hosts file backed up

REM Add entries
echo. >> "%SystemRoot%\System32\drivers\etc\hosts"
echo # 51Club Casino Local Development >> "%SystemRoot%\System32\drivers\etc\hosts"
echo 127.0.0.1    51club.local >> "%SystemRoot%\System32\drivers\etc\hosts"
echo 127.0.0.1    www.51club.local >> "%SystemRoot%\System32\drivers\etc\hosts"
echo 127.0.0.1    api.51club.local >> "%SystemRoot%\System32\drivers\etc\hosts"
echo 127.0.0.1    games.51club.local >> "%SystemRoot%\System32\drivers\etc\hosts"
echo 127.0.0.1    admin.51club.local >> "%SystemRoot%\System32\drivers\etc\hosts"
echo 127.0.0.1    db.51club.local >> "%SystemRoot%\System32\drivers\etc\hosts"

echo [OK] Domain entries added to hosts file

echo.
echo Flushing DNS cache...
ipconfig /flushdns >nul 2>&1
echo [OK] DNS cache flushed

echo.
echo ========================================
echo   Setup Complete!
echo ========================================
echo.
echo You can now access the services using clean URLs:
echo.
echo   Main Website:  http://51club.local:8085
echo   API Service:   http://api.51club.local:8085
echo   Games API:     http://games.51club.local:8085
echo   Admin Panel:   http://admin.51club.local:8085
echo   Database:      http://db.51club.local:8085
echo.
echo Service Directory: http://localhost:8085
echo.

pause
