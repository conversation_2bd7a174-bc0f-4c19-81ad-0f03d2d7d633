@echo off
echo 🌐 Setting up clean URLs for 51Club Casino...
echo.
echo This script will add the following domains to your hosts file:
echo   - 51club.local
echo   - api.51club.local  
echo   - games.51club.local
echo   - admin.51club.local
echo   - db.51club.local
echo.
echo ⚠️  This requires Administrator privileges!
echo.
pause

REM Check for admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Administrator privileges confirmed.
) else (
    echo ❌ This script requires Administrator privileges.
    echo Please right-click and "Run as Administrator"
    pause
    exit /b 1
)

REM Backup current hosts file
copy "%SystemRoot%\System32\drivers\etc\hosts" "%SystemRoot%\System32\drivers\etc\hosts.backup.%date:~-4,4%%date:~-10,2%%date:~-7,2%" >nul 2>&1
echo ✅ Hosts file backed up.

REM Remove any existing 51club entries
findstr /v "51club.local" "%SystemRoot%\System32\drivers\etc\hosts" > "%temp%\hosts_temp"
move "%temp%\hosts_temp" "%SystemRoot%\System32\drivers\etc\hosts" >nul 2>&1

REM Add new 51club entries
echo. >> "%SystemRoot%\System32\drivers\etc\hosts"
echo # 51Club Casino Local Development >> "%SystemRoot%\System32\drivers\etc\hosts"
echo 127.0.0.1    51club.local >> "%SystemRoot%\System32\drivers\etc\hosts"
echo 127.0.0.1    www.51club.local >> "%SystemRoot%\System32\drivers\etc\hosts"
echo 127.0.0.1    api.51club.local >> "%SystemRoot%\System32\drivers\etc\hosts"
echo 127.0.0.1    games.51club.local >> "%SystemRoot%\System32\drivers\etc\hosts"
echo 127.0.0.1    admin.51club.local >> "%SystemRoot%\System32\drivers\etc\hosts"
echo 127.0.0.1    db.51club.local >> "%SystemRoot%\System32\drivers\etc\hosts"

echo ✅ Hosts file updated successfully!
echo.
echo 🌐 The following URLs are now available:
echo   - http://51club.local (Main Website)
echo   - http://api.51club.local (API Service)
echo   - http://games.51club.local (Games API)
echo   - http://admin.51club.local (Admin Panel)
echo   - http://db.51club.local (phpMyAdmin)
echo.
echo 💡 Note: You may need to flush your DNS cache:
echo    ipconfig /flushdns
echo.
pause
