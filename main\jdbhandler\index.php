<?php


// Database configuration
$dbHost = "mysql";
$dbUser = "casino_user";
$dbPass = "casino_pass_2024";
$dbName = "casino_db";
error_reporting(E_ALL);
ini_set('display_errors', 1);
// Establish database connection
$conn = new mysqli($dbHost, $dbUser, $dbPass, $dbName);

// Check the connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Fetch POST data
$gameCode = $_GET['gameCode'] ?? '';
$userID = $_GET['userID'] ?? '';
$userPhone = $_GET['userPhone'] ?? '';

if (empty($gameCode) || empty($userID) || empty($userPhone)) {
    die('Invalid input data.');
}

// Step 1: Fetch the user's phone from shonu_subjects
$phoneQuery = "SELECT `mobile` FROM `shonu_subjects` WHERE `id` = ?";
$phoneStmt = $conn->prepare($phoneQuery);
$phoneStmt->bind_param("s", $userID);
$phoneStmt->execute();
$phoneResult = $phoneStmt->get_result();

if ($phoneResult->num_rows === 0) {
    die("Phone number not found for user ID: $userID.");
}

$phoneRow = $phoneResult->fetch_assoc();
$userPhone = $phoneRow['mobile'];

// Step 2: Fetch user's money from shonu_kaichila
$moneyQuery = "SELECT `motta` FROM `shonu_kaichila` WHERE `balakedara` = ?";
$moneyStmt = $conn->prepare($moneyQuery);
$moneyStmt->bind_param("s", $userID);
$moneyStmt->execute();
$moneyResult = $moneyStmt->get_result();

if ($moneyResult->num_rows === 0) {
    die("Money not found for user ID: $userID.");
}

$moneyRow = $moneyResult->fetch_assoc();
$mottaToSave = $moneyRow['motta'];

// Step 3: Subtract motta in shonu_kaichila table
if ($mottaToSave > 0) {
    $updateMottaQuery = "UPDATE `shonu_kaichila` SET `motta` = 0 WHERE `balakedara` = ?";
    $updateMottaStmt = $conn->prepare($updateMottaQuery);
    $updateMottaStmt->bind_param("s", $userID);
    $updateMottaStmt->execute();
} else {
        $mottaToSave = 0;
}

// Step 4: Check and update or insert in users table
$userCheckQuery = "SELECT `money` FROM `users` WHERE `phone` = ?";
$userCheckStmt = $conn->prepare($userCheckQuery);
$userCheckStmt->bind_param("s", $userPhone);
$userCheckStmt->execute();
$userCheckResult = $userCheckStmt->get_result();

if ($userCheckResult->num_rows > 0) {
    // Update money in the users table
    $existingData = $userCheckResult->fetch_assoc();
    $newMoney = $existingData['money'] + $mottaToSave;

    $updateUserQuery = "UPDATE `users` SET `money` = ? WHERE `phone` = ?";
    $updateUserStmt = $conn->prepare($updateUserQuery);
    $updateUserStmt->bind_param("is", $newMoney, $userPhone);
    $updateUserStmt->execute();
} else {
    // Insert new record in the users table
    $insertUserQuery = "INSERT INTO `users` (`id`, `phone`, `money`) VALUES (?, ?, ?)";
    $insertUserStmt = $conn->prepare($insertUserQuery);
    $insertUserStmt->bind_param("ssi", $userID, $userPhone, $mottaToSave);
    $insertUserStmt->execute();
}


// Step 4: Redirect to the game URL

$iv = 'f1ab7cea8106a3e4';
$key = 'b4d70df8d5c2857c';
$serverUrl = 'https://jdb.51club.space';
$parent = 'sxmapiz';
$baseUrl = 'https://sxm.in.net/jdb/post';

// Extract the first two characters of mType
$firstTwoDigits = substr($gameCode, 0, 2);

// Determine gType based on the first digit or special conditions for '14' or '8'
if ($firstTwoDigits === '14' || $firstTwoDigits === '8') {
    $gType = 0;
} else {
    // Extract the first character of mType if it's not '14' or '8'
    $firstDigit = substr($gameCode, 0, 1);
    $gType = is_numeric($firstDigit) ? $firstDigit : 0;
}

// Construct the jdbUrl with the modified gType
$jdbUrl = "$baseUrl?iv=$iv&key=$key&uid=$userPhone&serverUrl=$serverUrl&parent=$parent&gType=$gType&mType=$gameCode";

header("Location: $jdbUrl");
exit;

// Close the database connection
$conn->close();
?>
