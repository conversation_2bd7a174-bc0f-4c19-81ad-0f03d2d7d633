# PowerShell script to setup hosts file for 51Club Casino clean URLs
Write-Host "🌐 Setting up clean URLs for 51Club Casino..." -ForegroundColor Green
Write-Host ""

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCur<PERSON>()).IsIn<PERSON><PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ This script requires Administrator privileges." -ForegroundColor Red
    Write-Host "Please run PowerS<PERSON> as Administrator and try again." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✅ Administrator privileges confirmed." -ForegroundColor Green

# Define hosts file path
$hostsPath = "$env:SystemRoot\System32\drivers\etc\hosts"

# Backup current hosts file
$backupPath = "$hostsPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
Copy-Item $hostsPath $backupPath
Write-Host "✅ Hosts file backed up to: $backupPath" -ForegroundColor Green

# Read current hosts file
$hostsContent = Get-Content $hostsPath

# Remove any existing 51club entries
$cleanedContent = $hostsContent | Where-Object { $_ -notmatch "51club\.local" }

# Add new 51club entries
$newEntries = @(
    "",
    "# 51Club Casino Local Development",
    "127.0.0.1    51club.local",
    "127.0.0.1    www.51club.local", 
    "127.0.0.1    api.51club.local",
    "127.0.0.1    games.51club.local",
    "127.0.0.1    admin.51club.local",
    "127.0.0.1    db.51club.local"
)

# Combine content
$finalContent = $cleanedContent + $newEntries

# Write to hosts file
$finalContent | Out-File -FilePath $hostsPath -Encoding ASCII

Write-Host "✅ Hosts file updated successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 The following URLs will be available:" -ForegroundColor Cyan
Write-Host "   - http://51club.local (Main Website)" -ForegroundColor White
Write-Host "   - http://api.51club.local (API Service)" -ForegroundColor White
Write-Host "   - http://games.51club.local (Games API)" -ForegroundColor White
Write-Host "   - http://admin.51club.local (Admin Panel)" -ForegroundColor White
Write-Host "   - http://db.51club.local (phpMyAdmin)" -ForegroundColor White
Write-Host ""
Write-Host "💡 Flushing DNS cache..." -ForegroundColor Yellow
ipconfig /flushdns | Out-Null
Write-Host "✅ DNS cache flushed!" -ForegroundColor Green
Write-Host ""
Write-Host "🎉 Setup complete! You can now use clean URLs." -ForegroundColor Green
