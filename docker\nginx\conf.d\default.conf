# Clean URL Configuration for 51Club Casino

# Default server for localhost access
server {
    listen 80 default_server;
    server_name localhost _;

    # Security Headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Custom headers for debugging
    add_header X-Service "Default-Localhost" always;

    # Default landing page with service links
    location / {
        return 200 '<!DOCTYPE html>
<html>
<head>
    <title>51Club Casino - Service Directory</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .service-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 30px; }
        .service-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }
        .service-card h3 { margin-top: 0; color: #007bff; }
        .service-card a { color: #007bff; text-decoration: none; font-weight: bold; }
        .service-card a:hover { text-decoration: underline; }
        .note { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎰 51Club Casino - Service Directory</h1>
        <p>Welcome to the 51Club Casino development environment. Choose a service to access:</p>

        <div class="service-grid">
            <div class="service-card">
                <h3>🏠 Main Website</h3>
                <p>Primary casino website with games and user interface</p>
                <a href="http://51club.local:8085">http://51club.local:8085</a>
            </div>

            <div class="service-card">
                <h3>🔌 API Service</h3>
                <p>Backend API for casino operations and user management</p>
                <a href="http://api.51club.local:8085">http://api.51club.local:8085</a>
            </div>

            <div class="service-card">
                <h3>🎮 Games API</h3>
                <p>Node.js API for game providers (Jili, JDB, Jet)</p>
                <a href="http://games.51club.local:8085">http://games.51club.local:8085</a>
            </div>

            <div class="service-card">
                <h3>⚙️ Admin Panel</h3>
                <p>Administrative interface for casino management</p>
                <a href="http://admin.51club.local:8085">http://admin.51club.local:8085</a>
            </div>

            <div class="service-card">
                <h3>🗄️ Database Admin</h3>
                <p>phpMyAdmin interface for database management</p>
                <a href="http://db.51club.local:8085">http://db.51club.local:8085</a>
            </div>
        </div>

        <div class="note">
            <strong>Note:</strong> If the above links don\'t work, you need to add entries to your hosts file.
            Run the <code>update-hosts.bat</code> file as Administrator to configure domain routing.
        </div>
    </div>
</body>
</html>';
        add_header Content-Type text/html;
    }
}
# Main website - 51club.local
server {
    listen 80;
    server_name 51club.local www.51club.local;

    # Security Headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Custom headers for debugging
    add_header X-Service "Main-Website" always;

    location / {
        proxy_pass http://main:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_buffering off;
        proxy_request_buffering off;
    }
}

# API service - api.51club.local
server {
    listen 80;
    server_name api.51club.local;

    # API Security Headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Custom headers for debugging
    add_header X-Service "API-Service" always;

    # Rate limiting for API
    limit_req zone=api burst=20 nodelay;

    location / {
        proxy_pass http://api:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_buffering off;
        proxy_request_buffering off;
    }
}

# Games API service - games.51club.local
server {
    listen 80;
    server_name games.51club.local;

    # Security Headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Custom headers for debugging
    add_header X-Service "Games-API" always;

    # Rate limiting for Games API
    limit_req zone=api burst=30 nodelay;

    location / {
        proxy_pass http://api_games:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_buffering off;
        proxy_request_buffering off;
    }
}

# Admin panel - admin.51club.local
server {
    listen 80;
    server_name admin.51club.local;

    # Admin Security Headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Custom headers for debugging
    add_header X-Service "Admin-Panel" always;

    # Rate limiting for admin login
    location /login {
        limit_req zone=login burst=5 nodelay;
        proxy_pass http://adminx:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Forwarded-Host $server_name;
    }

    location / {
        proxy_pass http://adminx:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_buffering off;
        proxy_request_buffering off;
    }
}

# phpMyAdmin - db.51club.local
server {
    listen 80;
    server_name db.51club.local;

    # Database Security Headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Custom headers for debugging
    add_header X-Service "phpMyAdmin" always;

    # Rate limiting for database access
    limit_req zone=login burst=10 nodelay;

    location / {
        proxy_pass http://phpmyadmin:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_buffering off;
        proxy_request_buffering off;
    }
}
