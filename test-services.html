<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>51Club Casino - Service Tester</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .service-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 30px; }
        .service-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }
        .service-card h3 { margin-top: 0; color: #007bff; }
        .test-btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        .test-btn:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .hosts-info { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎰 51Club Casino - Service Tester</h1>
        <p>Test all casino services to verify they're working correctly:</p>
        
        <div class="service-grid">
            <div class="service-card">
                <h3>🏠 Main Website</h3>
                <p>Primary casino website</p>
                <button class="test-btn" onclick="testService('main', 'http://localhost:8085', '51club.local')">Test Main Site</button>
                <div id="result-main" class="result" style="display:none;"></div>
            </div>
            
            <div class="service-card">
                <h3>🔌 API Service</h3>
                <p>Backend API service</p>
                <button class="test-btn" onclick="testService('api', 'http://localhost:8085', 'api.51club.local')">Test API</button>
                <div id="result-api" class="result" style="display:none;"></div>
            </div>
            
            <div class="service-card">
                <h3>🎮 Games API</h3>
                <p>Node.js games API</p>
                <button class="test-btn" onclick="testService('games', 'http://localhost:8085/getUserBalance?userId=test', 'games.51club.local')">Test Games API</button>
                <div id="result-games" class="result" style="display:none;"></div>
            </div>
            
            <div class="service-card">
                <h3>⚙️ Admin Panel</h3>
                <p>Administrative interface</p>
                <button class="test-btn" onclick="testService('admin', 'http://localhost:8085', 'admin.51club.local')">Test Admin</button>
                <div id="result-admin" class="result" style="display:none;"></div>
            </div>
            
            <div class="service-card">
                <h3>🗄️ Database Admin</h3>
                <p>phpMyAdmin interface</p>
                <button class="test-btn" onclick="testService('db', 'http://localhost:8085', 'db.51club.local')">Test phpMyAdmin</button>
                <div id="result-db" class="result" style="display:none;"></div>
            </div>
        </div>
        
        <div class="hosts-info">
            <h4>📝 To enable clean URLs (optional):</h4>
            <p>Add these entries to your <code>C:\Windows\System32\drivers\etc\hosts</code> file:</p>
            <pre>127.0.0.1    51club.local
127.0.0.1    api.51club.local
127.0.0.1    games.51club.local
127.0.0.1    admin.51club.local
127.0.0.1    db.51club.local</pre>
            <p><strong>Note:</strong> You need Administrator privileges to edit the hosts file.</p>
        </div>
        
        <div style="margin-top: 20px; text-align: center;">
            <button class="test-btn" onclick="testAllServices()" style="background: #28a745;">Test All Services</button>
        </div>
    </div>

    <script>
        async function testService(serviceName, url, hostHeader) {
            const resultDiv = document.getElementById(`result-${serviceName}`);
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = 'Testing...';
            
            try {
                // Note: Due to CORS restrictions, we can't set custom Host headers from browser
                // This is a limitation of browser security. The test will show if the service responds.
                const response = await fetch(url, {
                    method: 'GET',
                    mode: 'cors'
                });
                
                const status = response.status;
                const statusText = response.statusText;
                
                if (status === 200) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ SUCCESS: ${status} ${statusText}`;
                } else if (status === 301 || status === 302) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ REDIRECT: ${status} ${statusText} (Expected for PHP services)`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `⚠️ STATUS: ${status} ${statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ ERROR: ${error.message}`;
            }
        }
        
        async function testAllServices() {
            await testService('main', 'http://localhost:8085', '51club.local');
            await new Promise(resolve => setTimeout(resolve, 500));
            await testService('api', 'http://localhost:8085', 'api.51club.local');
            await new Promise(resolve => setTimeout(resolve, 500));
            await testService('games', 'http://localhost:8085', 'games.51club.local');
            await new Promise(resolve => setTimeout(resolve, 500));
            await testService('admin', 'http://localhost:8085', 'admin.51club.local');
            await new Promise(resolve => setTimeout(resolve, 500));
            await testService('db', 'http://localhost:8085', 'db.51club.local');
        }
    </script>
</body>
</html>
