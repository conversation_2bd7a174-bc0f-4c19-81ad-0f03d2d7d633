#!/bin/bash

echo "🔧 Fixing SSL certificates for 51Club Casino..."

# Wait for nginx container to be running
echo "Waiting for nginx container to be ready..."
sleep 10

# Generate SSL certificates for all domains
DOMAINS=("51club.local" "api.51club.local" "games.51club.local" "admin.51club.local" "db.51club.local")

for domain in "${DOMAINS[@]}"; do
    echo "Generating SSL certificate for $domain..."
    docker exec 51club_nginx openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout "/etc/nginx/ssl/$domain.key" \
        -out "/etc/nginx/ssl/$domain.crt" \
        -subj "/C=US/ST=State/L=City/O=51Club/CN=$domain" 2>/dev/null
done

echo "Restarting nginx..."
docker-compose restart nginx

echo "✅ SSL certificates generated and nginx restarted!"
echo "🌐 Services should now be accessible at:"
echo "   - Main Site: https://localhost:8445 (or http://localhost:8085)"
echo "   - Database: http://localhost:3308 (phpMyAdmin via container)"
echo "   - Games API: http://localhost:3000 (via container)"
