#!/bin/bash

# SSL Certificate Generation Script for 51Club Casino
echo "🔐 Generating SSL certificates for all 51Club services..."

# Create SSL directory if it doesn't exist
mkdir -p /etc/nginx/ssl

# Define domains
DOMAINS=(
    "51club.local"
    "api.51club.local"
    "games.51club.local"
    "admin.51club.local"
    "db.51club.local"
)

# Generate CA private key
echo "📋 Generating Certificate Authority (CA)..."
openssl genrsa -out /etc/nginx/ssl/ca.key 4096

# Generate CA certificate
openssl req -new -x509 -days 3650 -key /etc/nginx/ssl/ca.key -out /etc/nginx/ssl/ca.crt -subj "/C=US/ST=State/L=City/O=51Club Casino/OU=IT Department/CN=51Club CA"

# Create OpenSSL configuration for SAN certificates
cat > /etc/nginx/ssl/openssl.cnf << EOF
[req]
default_bits = 2048
prompt = no
distinguished_name = req_distinguished_name
req_extensions = v3_req

[req_distinguished_name]
C=US
ST=State
L=City
O=51Club Casino
OU=IT Department

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = 51club.local
DNS.2 = www.51club.local
DNS.3 = api.51club.local
DNS.4 = games.51club.local
DNS.5 = admin.51club.local
DNS.6 = db.51club.local
DNS.7 = localhost
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

# Generate certificates for each domain
for domain in "${DOMAINS[@]}"; do
    echo "🔑 Generating certificate for $domain..."
    
    # Generate private key
    openssl genrsa -out /etc/nginx/ssl/$domain.key 2048
    
    # Generate certificate signing request
    openssl req -new -key /etc/nginx/ssl/$domain.key -out /etc/nginx/ssl/$domain.csr -config /etc/nginx/ssl/openssl.cnf -subj "/C=US/ST=State/L=City/O=51Club Casino/OU=IT Department/CN=$domain"
    
    # Generate certificate signed by CA
    openssl x509 -req -in /etc/nginx/ssl/$domain.csr -CA /etc/nginx/ssl/ca.crt -CAkey /etc/nginx/ssl/ca.key -CAcreateserial -out /etc/nginx/ssl/$domain.crt -days 365 -extensions v3_req -extfile /etc/nginx/ssl/openssl.cnf
    
    # Clean up CSR file
    rm /etc/nginx/ssl/$domain.csr
    
    echo "✅ Certificate generated for $domain"
done

# Create a bundle certificate for browsers that need it
cat /etc/nginx/ssl/ca.crt > /etc/nginx/ssl/ca-bundle.crt

# Set proper permissions
chmod 600 /etc/nginx/ssl/*.key
chmod 644 /etc/nginx/ssl/*.crt

echo "🎉 All SSL certificates generated successfully!"
echo ""
echo "📋 Generated certificates:"
for domain in "${DOMAINS[@]}"; do
    echo "  - $domain.crt / $domain.key"
done
echo ""
echo "🔐 Certificate Authority: ca.crt"
echo "📦 CA Bundle: ca-bundle.crt"
echo ""
echo "💡 To trust these certificates in your browser:"
echo "   1. Import ca.crt as a trusted root certificate"
echo "   2. Or accept the security warning for each domain"
